import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/post/sub_commnet/sub_comment.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_common_image_icon/app_common_image_icon.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:visibility_detector/visibility_detector.dart';

class CommentCard extends StatefulWidget {
  final PostDetail postDetail;
  final Function onTapEdit;
  final Function onTapDelete;
  final Function onTapReport;
  final Function onTapHeart;
  final Function onTapDrawer;
  final Function onTapProfileImage;
  final Function onTapShare;
  final Function onTapPost;
  final Function onTapReply;
  final SinglePostViewBloc singlePostViewBloc;

  // final bool isFromSinglePost;

  const CommentCard({
    super.key,
    required this.postDetail,
    required this.onTapEdit,
    required this.onTapDelete,
    required this.onTapReport,
    required this.onTapHeart,
    required this.onTapDrawer,
    required this.onTapProfileImage,
    required this.onTapShare,
    required this.onTapPost,
    required this.onTapReply,
    required this.singlePostViewBloc,
    // this.isFromSinglePost = false
  });

  @override
  State<CommentCard> createState() => _CommentCardState();
}

class _CommentCardState extends State<CommentCard> {
  //region Bloc
  late PostCardBloc postCardBloc;
  bool _showReplyField = false;

  //endregion

  //region Init
  @override
  void initState() {
    postCardBloc = PostCardBloc(context, widget.postDetail);
    postCardBloc.init();
    super.initState();
  }

//endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        leadingIcon: AppImages.locationIcon,
        isCustomLeadingIcon: true,
        onTapLeading: () {},
        context: context,
        isCenterTitle: true,
        isCustomTitle: true,
        customTitleWidget: const Text("Hello"),
        isDefaultMenuVisible: false);
  }

  //endregion

  //region Body
  Widget body() {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          userInfo(),
          InkWell(
            highlightColor: Colors.transparent,
            onTap: () {
              widget.onTapPost();
            },
            onDoubleTap: () {
              //If already liked then return
              if (widget.postDetail.likeStatus!) {
                return;
              }
              widget.onTapHeart();
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                postImage(),
                comment(),
                // counts(),
                action(),
              ],
            ),
          ),
          //Reply field (shown when reply button is tapped)
          if (_showReplyField)
            replyField(),
          //Sub comment
          subComment()
        ],
      ),
    );
  }

//endregion

  //region Widget user info
  Widget userInfo() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: () {
              widget.onTapProfileImage();
            },
            child: CustomImageContainer(
              width: 32,
              height: 32,
              imageUrl: widget.postDetail.createdBy!.icon,
              imageType: widget.postDetail.createdBy!.entityType ==
                      EntityType.USER.name
                  ? CustomImageContainerType.user
                  : CustomImageContainerType.store,
            ),
            // child: ClipRRect(
            //   borderRadius: BorderRadius.circular(widget.postDetail.entityType == EntityType.USER.name?100:(0.4130 * 27)),
            //   child: SizedBox(
            //     height: 27,
            //     width: 27,
            //     child: extendedImage(widget.postDetail.icon, context, 100, 100, customPlaceHolder:widget.postDetail.entityType == EntityType.USER.name?AppImages.userPlaceHolder:AppImages.storePlaceHolder),
            //   ),
            // ),
          ),
          const SizedBox(
            width: 7,
          ),

          //Handle and time
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: InkWell(
                    onTap: () {
                      widget.onTapProfileImage();
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          widget.postDetail.createdBy!.handle!,
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.writingBlack0),
                          overflow: TextOverflow.ellipsis,
                        ),
                        VerifiedBadge(
                          width: 15,
                          height: 15,
                          subscriptionType:
                              widget.postDetail.createdBy!.subscriptionType,
                        ),
                      ],
                    ),
                  ),
                ),
                Text(
                  postCardBloc.convertDateFormat(
                      inputDateTimeString: widget.postDetail.createdDate!),
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.smallText(
                      textColor: AppColors.writingBlack1),
                ),
              ],
            ),
          ),

          //Question
          Visibility(
              visible: widget.postDetail.commentType!.trim().toUpperCase() ==
                  CommentEnums.QUESTION.name,
              child: SvgPicture.asset(
                AppImages.commentQuestion,
                height: 18,
                width: 18,
              )),
          //Rating
          Visibility(
              visible: widget.postDetail.commentType!.trim().toUpperCase() ==
                  CommentEnums.REVIEW.name,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${widget.postDetail.ratingCount ?? ""} ",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.writingBlack0),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SvgPicture.asset(
                    AppImages.star,
                    height: 18,
                    width: 18,
                  ),
                ],
              )),
          //External Review
          Visibility(
              visible: widget.postDetail.commentType!.trim().toUpperCase() ==
                  CommentEnums.EXTERNAL_REVIEW.name,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${widget.postDetail.ratingCount ?? ""} ",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.writingBlack0),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SvgPicture.asset(
                    AppImages.star,
                    height: 18,
                    width: 18,
                  ),
                  const SizedBox(width: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.brandBlack.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      "External",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.brandBlack),
                    ),
                  ),
                ],
              )),
          //Three dots
          // SizedBox(
          //     height: 18,
          //     width: 18,
          //     child: CupertinoButton(
          //       padding: EdgeInsets.zero,
          //       onPressed: () {
          //         widget.onTapDrawer();
          //       },
          //       child: RotatedBox(
          //           quarterTurns: 1,
          //           child: SvgPicture.asset(
          //             AppImages.commentOption,
          //             color: AppColors.appBlack,
          //             height: 18,
          //           )),
          //     ))
        ],
      ),
    );
  }

  //endregion

  //region Post image
  Widget postImage() {
    return widget.postDetail.images!.isNotEmpty
        ? Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(vertical: 5),
            padding: const EdgeInsets.symmetric(vertical: 2.5),
            child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  height: 113,
                  child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: widget.postDetail.images!.length,
                      itemBuilder: (context, index) {
                        // return Container(
                        //   margin: EdgeInsets.symmetric(horizontal: 10),
                        //
                        //   child: ClipRRect(
                        //     borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width * 0.05),
                        //     child: Container(
                        //       color: Colors.orange,width: 113,height: 113,),
                        //   ),
                        // );

                        return CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              postCardBloc.onTapImage(
                                  index: index,
                                  imageList: widget.postDetail.images!
                                      .map((e) => e.mediaPath!)
                                      .toList());
                            },
                            child: PostAndProductImageWidgets(
                              localOrNetworkImage:
                                  widget.postDetail.images![index].mediaPath!,
                              imageSize: 113,
                            ));
                      }),
                )),
          )
        : const SizedBox();
  }

  //endregion

  //region Comment
  Widget comment() {
    return Visibility(
      visible:
          widget.postDetail.text != null && widget.postDetail.text!.isNotEmpty,
      child: Container(
        alignment: Alignment.centerLeft,
        margin: widget.postDetail.images!.isNotEmpty
            ? const EdgeInsets.only(left: 15, right: 15, bottom: 5)
            : const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
        child: ReadMoreText(
          widget.postDetail.text!,
          trimMode: TrimMode.Line,
          trimLines: 3,
          colorClickableText: Colors.pink,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          lessStyle:
              AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          moreStyle:
              AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          trimLength: 5,
          trimCollapsedText: AppStrings.more,
          trimExpandedText: " ${AppStrings.less}",
          textAlign: TextAlign.start,
          annotations: [
            // Annotation(
            //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
            //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
            //     text: text,
            //     style: textStyle?.copyWith(color: Colors.blue),
            //   ),
            // ),
            //User name or handle - Legacy format
            Annotation(
              regExp: RegExp(r"@[a-zA-Z0-9_]+(?:'s\s+[a-zA-Z0-9\s]+)?"),
              spanBuilder: ({required String text, TextStyle? textStyle}) =>
                  TextSpan(
                text: text,
                style: AppTextStyle.access0(textColor: AppColors.brandGreen),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    // Extract the username from the tapped text
                    final userName = text.substring(1);
                    OnTapTag(context, userName);
                    //print(userName); // Print the username
                  },
              ),
            ),
            //User name or handle - New encoded format
            Annotation(
              regExp: RegExp(r"\{\{mention:\{[^}]*\}\}\}"),
              spanBuilder: ({required String text, TextStyle? textStyle}) {
                // Extract display text from encoded mention
                String displayText = MentionParser.extractDisplayText(text);

                return TextSpan(
                  text: displayText,
                  style: AppTextStyle.access0(textColor: AppColors.brandGreen),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // Use the display text for navigation
                      OnTapTag(context, displayText);
                    },
                );
              },
            ),
            //URL
            Annotation(
              regExp: AppConstants.urlRegex,
              spanBuilder: ({required String text, TextStyle? textStyle}) =>
                  TextSpan(
                text: text,
                style: textStyle?.copyWith(color: AppColors.brandBlack),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    CommonMethods.openAppWebView(
                        webUrl: text,
                        context: AppConstants.globalNavigator.currentContext!);
                    //print(text); // Print the URL
                  },
              ),
            ),
          ],
        ),
        // child: ShowMoreAndLess(
        //   "${widget.postDetail.text!}",
        //   trimLines: 5,
        //   trimMode: TrimMode.Line,
        //   trimCollapsedText: AppStrings.more,
        //   trimExpandedText: " ${AppStrings.less}",
        //   textAlign: TextAlign.start,
        //   style:
        //        AppTextStyle.contentText0(textColor: AppColors.appBlack),
        //   lessStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
        //   moreStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
        // ),
      ),
    );
  }

  //endregion

  //region Counts
  Widget counts() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: Row(
        children: [
          Visibility(
            visible: widget.postDetail.likeCount != 0,
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: Text(
                  "${widget.postDetail.likeCount} ${widget.postDetail.likeCount == 1 ? "like" : "likes"}",
                  style: AppTextStyle.smallText(
                      textColor: AppColors.writingBlack1),
                )),
          ),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("50 comments", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          // const Expanded(child: SizedBox()),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("710 reposts", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("50 shares", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          //
        ],
      ),
    );
  }

  //endregion

  //region Action
  Widget action() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Like
          VisibilityDetector(
            key: UniqueKey(),
            onVisibilityChanged: (visibilityInfo) {
              var visiblePercentage = visibilityInfo.visibleFraction * 100;
              // If visibility is 100% and is visited is false
              if (visiblePercentage == 100 &&
                  !postCardBloc.postDetail.isVisited) {
                //postCardBloc.visited();
              }
            },
            child: InkWell(
              onTap: () {
                widget.onTapHeart();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 16,
                      width: 16,
                      child: SvgPicture.asset(
                        fit: BoxFit.fill,
                        widget.postDetail.likeStatus!
                            ? AppImages.postLike
                            : AppImages.postDisLike,
                        color: widget.postDetail.likeStatus!
                            ? AppColors.red
                            : AppColors.appBlack,
                      ),
                    ),
                    //Like count
                    Visibility(
                      visible: widget.postDetail.likeCount != 0,
                      child: Container(
                        margin: const EdgeInsets.only(left: 5),
                        child: Text(
                          "${widget.postDetail.likeCount}",
                          style: AppTextStyle.smallText(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          //Comment icon
          InkWell(
            onTap: () {
              setState(() {
                _showReplyField = !_showReplyField;
              });
              if (_showReplyField) {
                widget.onTapReply();
              }
            },
            child: Container(
              margin: const EdgeInsets.only(right: 15),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 16,
                    width: 16,
                    child: SvgPicture.asset(
                      fit: BoxFit.fill,
                      AppImages.postCommentReply,
                      color: AppColors.appBlack,
                    ),
                  ),
                  //Comment count
                  Visibility(
                    visible: widget.postDetail.commentCount != 0,
                    child: Container(
                      margin: const EdgeInsets.only(left: 5),
                      child: Text(
                        "${widget.postDetail.commentCount}",
                        style: AppTextStyle.smallText(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // AppToolTip(
          //   message: AppStrings.thisFeatureIsCommingSoon,
          //   toolTipWidget: Opacity(
          //     opacity: 0.2,
          //     child: Container(
          //       margin: const EdgeInsets.only(right: 15),
          //
          //         height: 16,
          //         width: 16,
          //         child: SvgPicture.asset(AppImages.postCommentReply, color: AppColors.appBlack)),
          //   ),
          // ),

          //Repost
          AppToolTip(
            message: AppStrings.thisFeatureIsCommingSoon,
            toolTipWidget: Opacity(
              opacity: 0.2,
              child: Container(
                  margin: const EdgeInsets.only(right: 15),
                  height: 16,
                  width: 16,
                  child: SvgPicture.asset(AppImages.repost,
                      color: AppColors.appBlack)),
            ),
          ),
          //Repost count
          Visibility(
            visible: widget.postDetail.repostCount != 0,
            child: Container(
              margin: const EdgeInsets.only(left: 5),
              child: Text(
                "${widget.postDetail.repostCount}",
                style:
                    AppTextStyle.smallText(textColor: AppColors.writingBlack1),
              ),
            ),
          ),

          //Option
          SizedBox(
            height: 16,
            width: 20,
            child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  postCardBloc.onTapDrawer(postDetail: widget.postDetail);
                },
                child: SvgPicture.asset(
                  AppImages.commentOption,
                  color: AppColors.appBlack,
                )),
          ),

          const Expanded(child: SizedBox()),
          //Time difference
          // Text(
          //   "${CommonMethods().formatTimeDifference(dateString: widget.postDetail.createdDate!)}",
          //   style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          // ),
        ],
      ),
    );
  }
//endregion

//region Sub comment
  Widget subComment() {
    return Visibility(
        //Make this visible only if comment count is more then 0
        visible: widget.postDetail.commentCount! > 0,
        child: Padding(
          padding: const EdgeInsets.only(left: 20),
          child: SubComments(
            commentDetail: widget.postDetail,
            singlePostViewBloc: widget.singlePostViewBloc,
          ),
        ));
  }
//endregion

  //region Reply field
  Widget replyField() {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Handle (replying to...)
          Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'replying to @${widget.postDetail.createdBy?.handle ?? ''}',
                    style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.writingBlack1),
                  ),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    setState(() {
                      _showReplyField = false;
                    });
                  },
                  child: Icon(
                    Icons.close,
                    size: 20,
                    color: AppColors.appBlack,
                  ),
                ),
              ],
            ),
          ),
          // Comment field
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              color: AppColors.appWhite,
              child: Row(
                children: [
                  Expanded(
                    child: AppTextFields.commentTextFields(
                      context: context,
                      textEditingController: widget.singlePostViewBloc.commentFieldsBloc.commentTextCtrl,
                      hintText: AppStrings.writeAComment,
                      maxLines: 5,
                      focusNode: widget.singlePostViewBloc.commentFieldsBloc.commentTextFocusCtrl,
                      border: 20,
                      minLines: 1,
                      maxEntry: 200,
                      textInputAction: TextInputAction.newline,
                      keyboardType: TextInputType.multiline,
                      onChanged: () {},
                    ),
                  ),
                  CupertinoButton(
                    padding: const EdgeInsets.only(right: 10),
                    onPressed: () {
                      // Send comment logic
                      widget.singlePostViewBloc.commentFieldsBloc.sendComment(
                        replyCommentOrPostDetail: {
                          "reference": widget.postDetail.postOrCommentReference,
                          "handle": widget.postDetail.createdBy?.handle ?? ""
                        },
                        commentEnums: CommentEnums.COMMENT,
                      );
                      setState(() {
                        _showReplyField = false;
                      });
                    },
                    child: Text(
                      AppStrings.send,
                      style: AppTextStyle.access0(textColor: AppColors.brandBlack),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
//endregion
}
