import 'package:flutter/material.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class TargetReferenceSearchWidget extends StatefulWidget {
  final Function(SuggestionItem?) onTargetSelected;
  final SuggestionItem? initialTarget;
  final String? hintText;
  final bool isEnabled;

  const TargetReferenceSearchWidget({
    Key? key,
    required this.onTargetSelected,
    this.initialTarget,
    this.hintText,
    this.isEnabled = true,
  }) : super(key: key);

  @override
  _TargetReferenceSearchWidgetState createState() => _TargetReferenceSearchWidgetState();
}

class _TargetReferenceSearchWidgetState extends State<TargetReferenceSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final TypingSuggestionsService _typingSuggestionsService = TypingSuggestionsService();
  
  List<SuggestionItem> _suggestions = [];
  bool _isLoading = false;
  bool _showSuggestions = false;
  SuggestionItem? _selectedTarget;

  @override
  void initState() {
    super.initState();
    _selectedTarget = widget.initialTarget;
    if (_selectedTarget != null) {
      _searchController.text = _selectedTarget!.primaryText ?? '';
    }
    
    _searchController.addListener(_onSearchChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    
    if (query.isEmpty) {
      setState(() {
        _suggestions.clear();
        _showSuggestions = false;
        _selectedTarget = null;
      });
      widget.onTargetSelected(null);
      return;
    }

    // If user is typing and we have a selected target, clear it
    if (_selectedTarget != null && _selectedTarget!.primaryText != query) {
      setState(() {
        _selectedTarget = null;
      });
      widget.onTargetSelected(null);
    }

    // Start search with @ prefix
    _searchSuggestions("@$query");
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      // Delay hiding suggestions to allow for selection
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) {
          setState(() {
            _showSuggestions = false;
          });
        }
      });
    }
  }

  Future<void> _searchSuggestions(String query) async {
    if (query.length < 2) return; // Minimum 2 characters including @

    setState(() {
      _isLoading = true;
    });

    try {
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      final response = await _typingSuggestionsService.getTypingSuggestions(
        query: query,
        limit: 10,
        offset: 0,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      if (mounted) {
        setState(() {
          _suggestions = response.results?.where((item) => 
            item.type == 'USER' || item.type == 'STORE'
          ).toList() ?? [];
          _showSuggestions = _suggestions.isNotEmpty;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _suggestions.clear();
          _showSuggestions = false;
          _isLoading = false;
        });
      }
    }
  }

  void _selectTarget(SuggestionItem target) {
    setState(() {
      _selectedTarget = target;
      _searchController.text = target.primaryText ?? '';
      _showSuggestions = false;
    });
    
    widget.onTargetSelected(target);
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        Container(
          decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _focusNode.hasFocus ? AppColors.brandBlack : Colors.transparent,
              width: 1,
            ),
          ),
          child: SizedBox(
            height: 43,
            child: TextField(
              controller: _searchController,
              focusNode: _focusNode,
              enabled: widget.isEnabled,
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Search users or stores...',
                hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                suffixIcon: _isLoading
                    ? const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : _selectedTarget != null
                        ? Icon(Icons.check_circle, color: AppColors.brandGreen)
                        : Icon(Icons.search, color: AppColors.writingBlack1),
              ),
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ),
        
        // Suggestions dropdown
        if (_showSuggestions && _suggestions.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _suggestions[index];
                return ListTile(
                  dense: true,
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: suggestion.type == 'USER' 
                        ? AppColors.appBlack.withOpacity(0.1)
                        : AppColors.brandGreen.withOpacity(0.1),
                    child: Icon(
                      suggestion.type == 'USER' ? Icons.person : Icons.store,
                      size: 16,
                      color: suggestion.type == 'USER' 
                          ? AppColors.appBlack
                          : AppColors.brandGreen,
                    ),
                  ),
                  title: Text(
                    suggestion.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  subtitle: suggestion.secondaryText != null
                      ? Text(
                          suggestion.secondaryText!,
                          style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
                        )
                      : null,
                  onTap: () => _selectTarget(suggestion),
                );
              },
            ),
          ),
      ],
    );
  }
}
