class AppStartupMessage {
  static bool? _showMessage;
  static String? _displayText;
  static String? _link;

  // Getters
  bool? get showMessage => _showMessage;
  String? get displayText => _displayText;
  String? get link => _link;

  AppStartupMessage({
    bool? showMessage,
    String? displayText,
    String? link,
  }) {
    if (showMessage != null) _showMessage = showMessage;
    if (displayText != null) _displayText = displayText;
    if (link != null) _link = link;
  }

  factory AppStartupMessage.fromJson(Map<String, dynamic> json) {
    _showMessage = json['show_message'] as bool? ?? false;
    _displayText = json['display_text'] as String?;
    _link = json['link'] as String?;
    
    return AppStartupMessage(
      showMessage: _showMessage,
      displayText: _displayText,
      link: _link,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_message': _showMessage,
      'display_text': _displayText,
      'link': _link,
    };
  }

  @override
  String toString() {
    return 'AppStartupMessage(showMessage: $_showMessage, displayText: $_displayText, link: $_link)';
  }
}
