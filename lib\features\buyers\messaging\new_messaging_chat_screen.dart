import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';

import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';

import 'package:swadesic/features/buyers/messaging/buffer/message_repository.dart';
import 'package:http/http.dart' as http;
import 'dart:developer' as developer;
import 'package:url_launcher/url_launcher.dart';
import 'package:file_picker/file_picker.dart';
import 'package:swadesic/features/buyers/messaging/utils/file_attachment_helpers.dart';
import 'package:swadesic/features/buyers/messaging/utils/url_helpers.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/object_preview_manager.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_common_widget.dart';


class NewMessagingChatScreen extends StatefulWidget {
  final String chat_id;
  final String chat_name;
  final String chat_icon;
  final String last_seen;
  final String connecting_id;
  final String entity_type;
  final MessageRepository? messageRepository;
  const NewMessagingChatScreen({
    Key? key,
    required this.chat_id,
    required this.chat_name,
    required this.chat_icon,
    required this.last_seen,
    required this.connecting_id,
    required this.entity_type,
    this.messageRepository,
  }) : super(key: key);

  @override
  State<NewMessagingChatScreen> createState() => _NewMessagingChatScreenState();

  /// Helper function to navigate to chat screen after checking if chat exists
  static Future<void> navigateToChat(
    BuildContext context, {
    required String connectingId,
    required String chatName,
    required String chatIcon,
    required String entityType,
    MessageRepository? messageRepository,
  }) async {
    try {
      developer.log('[ENTER] navigateToChat(): Preparing to navigate',
          name: 'NewMessagingChatScreen',
          error: {'connectingId': connectingId, 'chatName': chatName});

      final repo = messageRepository ?? MessageRepository();

      // First check if chat exists
      final existingChat = await repo.checkDirectChat(connectingId);

      if (!context.mounted) return;

      developer.log('[INFO] navigateToChat(): Chat check complete',
          name: 'NewMessagingChatScreen',
          error: {
            'chatExists': existingChat != null,
            'chatId': existingChat?.chat_id
          });

      // Use rootNavigator to push screen outside of PersistentTabView
      Navigator.of(context, rootNavigator: true).push(
        MaterialPageRoute(
          builder: (context) => NewMessagingChatScreen(
            chat_id: existingChat?.chat_id ?? '',
            chat_name: chatName,
            chat_icon: chatIcon,
            last_seen: 'Last seen recently',
            connecting_id: connectingId,
            messageRepository: repo,
            entity_type: entityType,
          ),
        ),
      );

      developer.log('[EXIT] navigateToChat(): Navigation complete',
          name: 'NewMessagingChatScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] navigateToChat(): Failed to navigate',
          name: 'NewMessagingChatScreen',
          error: {'error': e.toString(), 'connectingId': connectingId},
          stackTrace: stackTrace);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open chat: ${e.toString()}')),
        );
      }
    }
  }
}

class _NewMessagingChatScreenState extends State<NewMessagingChatScreen> {
  /// List of messages in the chat
  List<dynamic> _messages = [];
  late TextEditingController _messageController;
  late ScrollController _scrollController;
  late MessageRepository _messageRepository;
  WebSocketChannel? _channel;
  bool _isLoading = true;
  bool _showEmoji = false;
  bool _isLoadingMore = false;
  ChatInfo? _chatInfo;
  late _MessageStateManager _messageStateManager;
  final String _currentUserId =
      AppConstants.appData.newMessagingUserId?.toString() ?? '';

  // File upload related variables
  List<PlatformFile> _selectedFiles = [];
  Map<String, bool> _uploadingFiles = {};
  final int _maxFiles = 4; // Maximum 4 files can be uploaded at once

  // Timer for debouncing mark-as-read calls
  Timer? _markAsReadTimer;
  // Track last marked sequence to avoid duplicate calls
  int _lastMarkedSequence = 0;
  
  // Controller for chat name editing
  late final TextEditingController _nameController;

  @override
  void initState() {
    super.initState();

    developer.log('[ENTER] initState(): Initializing chat screen',
        name: 'ChatScreen',
        error: {
          'chat_id': widget.chat_id,
          'connecting_id': widget.connecting_id,
        });

    // Initialize controllers
    _messageController = TextEditingController();
    _scrollController = ScrollController();
    _messageRepository = widget.messageRepository ?? MessageRepository();
    _nameController = TextEditingController(text: widget.chat_name);

    // Initialize message state manager
    _messageStateManager = _MessageStateManager(
      messages: _messages,
      onMessagesChanged: (newMessages) {
        developer.log(
            '[INFO] MessageStateManager.onMessagesChanged(): Updating messages',
            name: 'ChatScreen',
            error: {
              'oldCount': _messages.length,
              'newCount': newMessages.length,
            });
        setState(() {
          _messages.clear();
          _messages.addAll(newMessages);
        });
      },
      onScrollToBottom: _scrollToBottom,
      currentUserId: _currentUserId,
    );

    // Setup scroll listener
    _setupScrollListener();

    // Initialize chat asynchronously but properly handle errors
    _initializeChat().catchError((e, stackTrace) {
      developer.log('[ERROR] initState(): Failed to initialize chat',
          name: 'ChatScreen',
          error: {
            'error': e.toString(),
          },
          stackTrace: stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to initialize chat: ${e.toString()}')),
        );
      }
    });

    developer.log('[EXIT] initState(): Chat screen initialized',
        name: 'ChatScreen');
  }

  @override
  void dispose() {
    _markAsReadTimer?.cancel();
    _channel?.sink.close();
    _messageController.dispose();
    _scrollController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Load older messages when near the top (scrolling up)
      if (_scrollController.position.pixels <=
              _scrollController.position.minScrollExtent + 200 &&
          !_isLoadingMore) {
        _loadMoreMessages(older: true);
      }
      // Load newer messages when near the bottom (scrolling down)
      else if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoadingMore) {
        _loadMoreMessages(older: false);
      }

      // Mark messages as read after scrolling
      _scheduleMarkAsRead();
    });
  }

  void _scheduleMarkAsRead() {
    if (_messages.isEmpty || _channel == null || !mounted) return;

    // Cancel existing timer
    _markAsReadTimer?.cancel();

    // Start new timer
    _markAsReadTimer = Timer(const Duration(seconds: 1), () {
      if (!mounted) return;

      try {
        // Calculate visible items
        final firstVisibleIndex =
            (_scrollController.position.pixels / 80.0).floor();
        final lastVisibleIndex = (((_scrollController.position.pixels +
                        MediaQuery.of(context).size.height) /
                    80.0) -
                1)
            .floor();

        if (firstVisibleIndex < 0 || lastVisibleIndex >= _messages.length)
          return;

        // Get sequence numbers of visible messages
        final visibleMessages = _messages.sublist(
            firstVisibleIndex.clamp(0, _messages.length - 1),
            lastVisibleIndex.clamp(0, _messages.length));

        if (visibleMessages.isEmpty) return;

        // Get the highest sequence number from visible messages
        final highestSequence = visibleMessages.fold<int>(
            0,
            (max, msg) =>
                msg.sequence_number > max ? msg.sequence_number : max);

        // Only send if this sequence is higher than last marked
        if (highestSequence > _lastMarkedSequence) {
          developer.log(
              '[INFO] _scheduleMarkAsRead(): Marking messages as read',
              name: 'ChatScreen',
              error: {
                'lastMarkedSequence': _lastMarkedSequence,
                'newSequence': highestSequence,
                'visibleMessageCount': visibleMessages.length,
              });

          _channel?.sink.add(jsonEncode({
            'type': 'mark_messages_as_read',
            'chat_id': widget.chat_id,
            'last_read_sequence': highestSequence,
          }));

          _lastMarkedSequence = highestSequence;
        }
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] _scheduleMarkAsRead(): Failed to mark messages as read',
            name: 'ChatScreen',
            error: {
              'error': e.toString(),
            },
            stackTrace: stackTrace);
      }
    });
  }

  Future<void> _initializeChat() async {
    setState(() => _isLoading = true);
    try {
      developer.log('[ENTER] _initializeChat(): Initializing chat',
          name: 'ChatScreen',
          error: {
            'chat_id': widget.chat_id,
            'connecting_id': widget.connecting_id,
          });

      await _connectWebSocket();

      developer.log('[INFO] _initializeChat(): WebSocket connected',
          name: 'ChatScreen');

      final doesChatExists = await _checkIfChatExists();
      if (doesChatExists) {
        developer.log('[INFO] _initializeChat(): Chat already exists',
            name: 'ChatScreen',
            error: {
              'chat_id': widget.chat_id,
            });
        // Load messages only after subscription is confirmed
        await _loadInitialMessages();
        return;
      }

      if (widget.chat_id.isNotEmpty) {
        developer.log('[INFO] _initializeChat(): Initializing existing chat',
            name: 'ChatScreen',
            error: {
              'chat_id': widget.chat_id,
            });

        // Initialize chat and wait for it to complete

        await _initializeExistingChat();
        developer.log('[INFO] _initializeChat(): Chat initialized, subscribing',
            name: 'ChatScreen',
            error: {
              'chat_info': _chatInfo?.toJson(),
            });

        developer.log(
            '[INFO] _initializeChat(): Chat subscribed, loading messages',
            name: 'ChatScreen');

        // Load messages
        await _loadInitialMessages();
      } else if (widget.connecting_id.isNotEmpty) {
        developer.log('[INFO] _initializeChat(): Creating new chat',
            name: 'ChatScreen',
            error: {
              'connecting_id': widget.connecting_id,
            });

        _chatInfo = ChatInfo(
          chat_id: '', // Will be set when first message is sent
          chat_icon: widget.chat_icon,
          chat_name: widget.chat_name,
          chat_type: 'DIRECT', // Default to direct chat
          user_id: AppConstants.appData.userId?.toString() ?? '',
          is_subscribed: false,
          unread_count: 0,
          last_read_sequence: 0,
          entity_type: widget.entity_type, // Use provided entity type
          created_at: DateTime.now(),
          updated_at: DateTime.now(),
          role_type: null,
          is_muted: false,
          connecting_id: widget.connecting_id,
          message_access: null, // Default message access for new chats
        );

        developer.log('[INFO] _initializeChat(): New chat info created',
            name: 'ChatScreen',
            error: {
              'chat_info': _chatInfo?.toJson(),
            });
      } else {
        throw Exception('Either chat_id or connecting_id must be provided');
      }

      developer.log('[EXIT] _initializeChat(): Chat initialization complete',
          name: 'ChatScreen',
          error: {
            'chat_info': _chatInfo?.toJson(),
          });
    } catch (e, stackTrace) {
      developer.log('[ERROR] _initializeChat(): Failed to initialize chat',
          name: 'ChatScreen',
          error: {
            'error': e.toString(),
            'chat_id': widget.chat_id,
            'connecting_id': widget.connecting_id,
          },
          stackTrace: stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to initialize chat: ${e.toString()}')),
        );
      }
      rethrow;
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _initializeExistingChat() async {
    try {
      developer.log('[ENTER] _initializeExistingChat(): Loading chat details',
          name: 'ChatScreen',
          error: {
            'chat_id': widget.chat_id,
          });

      // Try to get chat info from buffer
      _chatInfo =
          await _messageRepository.bufferManager.getChatInfo(widget.chat_id);

      if (_chatInfo != null) {
        developer.log('[INFO] _initializeExistingChat(): Found chat in buffer',
            name: 'ChatScreen',
            error: {
              'chat_info': _chatInfo?.toJson(),
            });
        return;
      }

      developer.log(
          '[INFO] _initializeExistingChat(): Chat not in buffer, loading from server',
          name: 'ChatScreen');

      // If not in buffer, load chat details from server
      final response = await http.get(
        Uri.parse('${AppConstants.newMessaging_getChatById}${widget.chat_id}'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode != 200) {
        developer.log(
            '[ERROR] _initializeExistingChat(): Failed to load chat details from server',
            name: 'ChatScreen',
            error: {
              'statusCode': response.statusCode,
              'body': response.body,
            });
        throw Exception(
            'Failed to load chat details: ${response.statusCode} - ${response.body}');
      }

      try {
        final data = jsonDecode(response.body);
        _chatInfo = ChatInfo.fromJson(data);
        await _messageRepository.bufferManager.addChat(_chatInfo!);

        developer.log(
            '[EXIT] _initializeExistingChat(): Chat loaded and cached',
            name: 'ChatScreen',
            error: {
              'chat_info': _chatInfo?.toJson(),
            });
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] _initializeExistingChat(): Failed to parse chat details',
            name: 'ChatScreen',
            error: {
              'error': e.toString(),
              'response': response.body,
            },
            stackTrace: stackTrace);
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _initializeExistingChat(): ${e.toString()}',
          name: 'ChatScreen',
          error: {
            'chat_id': widget.chat_id,
          },
          stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> _createTheChat() async {
    /// Creates a new chat and subscribes to it. Buffers the Chat.
    /// Returns the chat ID created.

    try {
      developer.log(
          '[ENTER] _createTheChat(): connecting_id=${widget.connecting_id}',
          name: 'NewMessagingChatScreen');

      // Validate messaging token
      final messagingToken = AppConstants.appData.newMessagingToken;
      if (messagingToken == null || messagingToken.isEmpty) {
        throw Exception('No messaging token available');
      }

      // Check if chat exists
      if (await _checkIfChatExists()) {
        return;
      }

      // No existing chat found, create new one
      // Validate connecting ID
      if (widget.connecting_id.isEmpty || widget.connecting_id == "") {
        throw Exception('Connecting ID is required for new chat');
      }

      final createBody = {
        'chat_type': 'DIRECT',
        'member_ids': [widget.connecting_id],
      };

      developer.log(
          '[INFO] _createAndSubscribeToChat(): Creating new chat with body: ${jsonEncode(createBody)}',
          name: 'NewMessagingChatScreen');

      final createResponse = await http.post(
        Uri.parse(AppConstants.newMessaging_createChat),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(createBody),
      );

      developer.log(
          '[INFO] _createAndSubscribeToChat(): Create chat response: ${createResponse.statusCode} - ${createResponse.body} ',
          name: 'NewMessagingChatScreen');

      // Handle response to create a Subscribe tot the chat
      if (createResponse.statusCode == 200 ||
          createResponse.statusCode == 201) {
        try {
          final responseData = jsonDecode(createResponse.body);
          final chatData = responseData['chat'] ?? responseData;

          developer.log(
              '[INFO] _createAndSubscribeToChat(): Chat created successfully',
              name: 'NewMessagingChatScreen');

          if (chatData == null || chatData['chat_id'] == null) {
            throw Exception('Invalid chat data received');
          }

          // Create ChatInfo using extracted data
          _chatInfo = ChatInfo(
            chat_id: chatData['chat_id'],
            chat_icon: widget.chat_icon,
            chat_name: widget.chat_name,
            chat_type: chatData['chat_type'] ?? 'DIRECT',
            user_id: AppConstants.appData.userId?.toString() ?? '',
            is_subscribed: false,
            unread_count: 0,
            last_read_sequence: 0,
            entity_type: widget.entity_type,
            created_at: DateTime.now(),
            updated_at: DateTime.now(),
            member_ids: [widget.connecting_id],
            role_type: null,
            is_muted: false,
            connecting_id: widget.connecting_id,
          );

          developer.log(
              '[INFO] _createAndSubscribeToChat(): ChatInfo created: ${_chatInfo!.toJson()}',
              name: 'NewMessagingChatScreen');

          // Save to buffer and subscribe
          await _messageRepository.bufferManager.addChat(_chatInfo!);

          developer.log(
              '[EXIT] _createAndSubscribeToChat(): Chat created and subscribed',
              name: 'NewMessagingChatScreen');
        } catch (e, stackTrace) {
          developer.log(
              '[ERROR] _createAndSubscribeToChat(): Error parsing create response: ${e.toString()}',
              name: 'NewMessagingChatScreen',
              stackTrace: stackTrace);
          developer.log(
              '[ERROR] Raw response that failed to parse: ${createResponse.body}',
              name: 'NewMessagingChatScreen');
          rethrow;
        }
      } else {
        final errorBody = createResponse.body;
        developer.log(
            '[ERROR] _createAndSubscribeToChat(): Server error response: $errorBody',
            name: 'NewMessagingChatScreen');
        throw Exception(
            'Failed to create chat: ${createResponse.statusCode} - $errorBody');
      }
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] _createAndSubscribeToChat(): Failed to create chat ${e.toString()}',
          name: 'NewMessagingChatScreen',
          stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<bool> _checkIfChatExists() async {
    try {
      developer.log(
          '[ENTER] _checkIfChatExists(): connecting_id=${widget.connecting_id}',
          name: 'NewMessagingChatScreen');

      final response = await http.get(
        Uri.parse(
            '${AppConstants.newMessaging_checkDirectChat}${widget.connecting_id}'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );
      developer.log(
          '[INFO] _checkIfChatExists(): Check direct chat response: ${response.statusCode} - ${response.body}',
          name: 'NewMessagingChatScreen');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['message'] == 'Chat already exists') {
          developer.log(
              '[INFO] _checkIfChatExists(): Found existing chat with ID ${data['chat']['chat_id']}',
              name: 'NewMessagingChatScreen');

          _chatInfo = ChatInfo.fromJson(data['chat']);
          await _messageRepository.bufferManager.addChat(_chatInfo!);

          return true;
        }
      }
      return false;
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] _checkIfChatExists(): Failed to check chat existence ${e.toString()}',
          name: 'NewMessagingChatScreen',
          stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> _subscribeToChat() async {
    try {
      developer.log(
          '[ENTER] _subscribeToChat(): Subscribing to chat ${_chatInfo?.chat_id}',
          name: 'NewMessagingChatScreen');

      if (_chatInfo == null) {
        developer.log(
            '[ERROR] _subscribeToChat(): Cannot subscribe to chat: _chatInfo is null',
            name: 'NewMessagingChatScreen');
        return;
      }

      // Always send subscription message to ensure we're subscribed
      final subscribeMessage = {
        'type': 'subscribe_chat',
        'chat_id': _chatInfo!.chat_id,
      };

      developer.log(
          '[INFO] _subscribeToChat(): Sending subscription message: ${jsonEncode(subscribeMessage)}',
          name: 'NewMessagingChatScreen');
      // making sure we are connected to the websocket
      await _connectWebSocket();
      _channel?.sink.add(jsonEncode(subscribeMessage));

      developer.log('[EXIT] _subscribeToChat(): Subscription message sent',
          name: 'NewMessagingChatScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _subscribeToChat(): ${e.toString()}',
          name: 'NewMessagingChatScreen', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> _loadInitialMessages() async {
    try {
      developer.log('[ENTER] _loadInitialMessages(): Loading initial messages',
          name: 'ChatScreen',
          error: {
            'chat_id': _chatInfo?.chat_id,
            'last_read_sequence': _chatInfo?.last_read_sequence,
            'is_subscribed': _chatInfo?.is_subscribed,
          });

      if (_chatInfo == null) {
        throw Exception('Cannot load messages: Chat info is not initialized');
      }

      final messages = await _messageRepository.getMessagesAroundSequence(
        _chatInfo!.chat_id,
        _chatInfo!.last_read_sequence ?? 0,
      );

      try {
        developer.log(
            '[INFO] _loadInitialMessages(): Parsing and processing messages',
            name: 'ChatScreen',
            error: {
              'messageCount': messages.length,
            });

        _messageStateManager.setInitialMessages(messages);

        // Don't auto-scroll when initially loading messages

        // Find the last read message index
        final lastReadIndex = messages.indexWhere(
            (m) => m.sequence_number == _chatInfo!.last_read_sequence);

        if (lastReadIndex != -1) {
          // Wait for the list to be built
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              // Calculate the position to show the last read message
              final itemHeight = 80.0; // Approximate height of a message item
              final screenHeight = MediaQuery.of(context).size.height;
              final offset = (lastReadIndex * itemHeight) - (screenHeight / 3);

              _scrollController.jumpTo(offset.clamp(
                  0.0, _scrollController.position.maxScrollExtent));
            }
          });
        }

        developer.log(
            '[EXIT] _loadInitialMessages(): Messages loaded and displayed',
            name: 'ChatScreen',
            error: {
              'finalMessageCount': messages.length,
            });
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] _loadInitialMessages(): Failed to process messages',
            name: 'ChatScreen',
            error: {
              'error': e.toString(),
              'rawMessages': messages,
            },
            stackTrace: stackTrace);
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _loadInitialMessages(): Failed to load messages',
          name: 'ChatScreen',
          error: {
            'error': e.toString(),
            'chat_id': _chatInfo?.chat_id,
          },
          stackTrace: stackTrace);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                '[new_messaging_chat_screen._loadInitialMessages] Failed to load messages: ${e.toString()}')),
      );
      rethrow;
    }
  }

  Future<void> _loadMoreMessages({required bool older}) async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    try {
      developer.log('[ENTER] _loadMoreMessages(): Loading more messages',
          name: 'ChatScreen',
          error: {
            'chat_id': widget.chat_id,
            'loading_older': older,
          });

      final newMessages = await _messageRepository
          .loadMoreMessages(widget.chat_id, older: older);

      if (newMessages.isEmpty) {
        developer.log('[INFO] _loadMoreMessages(): No more messages to load',
            name: 'ChatScreen');
        return;
      }

      try {
        developer.log('[INFO] _loadMoreMessages(): Processing new messages',
            name: 'ChatScreen',
            error: {
              'messageCount': newMessages.length,
            });

        // Store current scroll position before adding messages
        final currentPosition = _scrollController.position.pixels;

        // Add messages to state manager
        _messageStateManager.addMoreMessages(newMessages, older: older);

        // Restore scroll position after messages are added
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _scrollController.jumpTo(currentPosition);
          }
        });

        developer.log(
            '[EXIT] _loadMoreMessages(): Messages loaded and displayed',
            name: 'ChatScreen',
            error: {
              'messageCount': newMessages.length,
            });
      } catch (e, stackTrace) {
        developer.log('[ERROR] _loadMoreMessages(): Failed to process messages',
            name: 'ChatScreen',
            error: {
              'error': e.toString(),
            },
            stackTrace: stackTrace);
        developer.log(
            '[ERROR] _loadMoreMessages(): Raw messages that failed to process',
            name: 'ChatScreen',
            error: {
              'rawMessages': newMessages.map((m) => m.toJson()).toList(),
            });
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _loadMoreMessages(): Failed to load messages',
          name: 'ChatScreen',
          error: {
            'error': e.toString(),
            'chat_id': widget.chat_id,
          },
          stackTrace: stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  '[new_messaging_chat_screen._loadMoreMessages] Failed to load more messages: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  Future<void> _connectWebSocket() async {
    try {
      developer.log(
          '[new_messaging_chat_screen:_connectWebSocket] Starting WebSocket connection');
      if (_channel != null) {
        developer.log(
            '[new_messaging_chat_screen:_connectWebSocket] WebSocket connection already exists, not reconnecting');
        return;
      }

      final token = AppConstants.appData.newMessagingToken;
      if (token == null || token.isEmpty) {
        throw Exception('No messaging token available');
      }

      developer.log(
          '[new_messaging_chat_screen:_connectWebSocket] Connecting to WebSocket with token: $token',
          name: 'NewMessagingChatScreen');

      final wsUrl =
          Uri.parse('${AppConstants.newMessaging_webSocketConnection}$token');
      developer.log(
          '[new_messaging_chat_screen:_connectWebSocket] WebSocket URL: $wsUrl',
          name: 'NewMessagingChatScreen');

      try {
        _channel = WebSocketChannel.connect(wsUrl);
        developer.log(
            '[new_messaging_chat_screen:_connectWebSocket] WebSocket connection established',
            name: 'NewMessagingChatScreen');
      } catch (e, stackTrace) {
        developer.log(
            '[new_messaging_chat_screen:_connectWebSocket] Failed to connect to WebSocket: ${e.toString()}',
            name: 'NewMessagingChatScreen',
            stackTrace: stackTrace);
        rethrow;
      }

      // Set up stream listener
      _channel?.stream.listen(
        (dynamic message) {
          try {
            developer.log(
                '[new_messaging_chat_screen:_connectWebSocket] Received WebSocket message: $message',
                name: 'NewMessagingChatScreen');

            final data = jsonDecode(message.toString());
            developer.log(
                '[new_messaging_chat_screen:_connectWebSocket] Parsed WebSocket message: ${jsonEncode(data)}',
                name: 'NewMessagingChatScreen');

            _handleWebSocketMessage(data);
          } catch (e, stackTrace) {
            developer.log(
                '[new_messaging_chat_screen:_connectWebSocket] Error parsing WebSocket message: $e',
                name: 'NewMessagingChatScreen',
                stackTrace: stackTrace);
          }
        },
        onError: (error, stackTrace) {
          developer.log(
              '[new_messaging_chat_screen:_connectWebSocket] WebSocket Error: $error',
              name: 'NewMessagingChatScreen',
              stackTrace: stackTrace);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Connection error: ${error.toString()}')),
          );
          // Try to reconnect
          Future.delayed(const Duration(seconds: 2), _connectWebSocket);
        },
        onDone: () {
          developer.log(
              '[new_messaging_chat_screen:_connectWebSocket] WebSocket connection closed',
              name: 'NewMessagingChatScreen');
          // Try to reconnect
          Future.delayed(const Duration(seconds: 2), _connectWebSocket);
        },
        cancelOnError: false,
      );

      // Send initial presence message
      _channel?.sink.add(jsonEncode({
        'type': 'presence',
        'status': 'online',
      }));
    } catch (e) {
      developer.log(
          '[new_messaging_chat_screen:_connectWebSocket] WebSocket connection error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to connect: ${e.toString()}')),
      );
      // Try to reconnect
      Future.delayed(const Duration(seconds: 2), _connectWebSocket);
    }
  }

  void _handleWebSocketMessage(dynamic message) async {
    try {
      developer.log(
          '[ENTER] _handleWebSocketMessage(): Processing WebSocket message',
          name: 'NewMessagingChatScreen');

      // First parse raw message with jsonDecode if needed
      final Map<String, dynamic> data =
          message is String ? jsonDecode(message) : message;

      developer.log(
          '[INFO] _handleWebSocketMessage(): Processing message data: ${jsonEncode(data)}',
          name: 'NewMessagingChatScreen');

      if (data['type'] == 'error') {
        developer.log(
            '[ERROR] _handleWebSocketMessage(): WebSocket error: ${data['message']}',
            name: 'NewMessagingChatScreen');
        return;
      }

      // Handle different message types
      switch (data['type']) {
        case 'welcome':
          developer.log(
              '[INFO] _handleWebSocketMessage(): Received welcome message: ${data['message']}',
              name: 'NewMessagingChatScreen');
          break;

        case 'new_message':
          final messageData = data['message'];
          if (messageData != null) {
            try {
              final newMessage = Message(
                message_id: messageData['message_id']?.toString() ?? '',
                chat_id: messageData['chat_id']?.toString() ?? '',
                sender_id: messageData['sender_id']?.toString() ?? '',
                content: messageData['content']?.toString() ?? '',
                message_type: messageData['message_type']?.toString() ?? 'text',
                metadata:
                    messageData['metadata'] as Map<String, dynamic>? ?? {},
                attachments: messageData['attachments'] as List<dynamic>? ?? [],
                sequence_number: int.tryParse(
                        messageData['sequence_number']?.toString() ?? '0') ??
                    0,
                created_at: DateTime.tryParse(
                        messageData['created_at']?.toString() ?? '') ??
                    DateTime.now(),
                updated_at: DateTime.tryParse(
                        messageData['updated_at']?.toString() ?? '') ??
                    DateTime.now(),
                sender_name: messageData['sender_name']?.toString(),
              );

              developer.log(
                  '[INFO] _handleWebSocketMessage(): New message received',
                  name: 'NewMessagingChatScreen');

              _messageStateManager.handleNewMessage(newMessage);
            } catch (e, stackTrace) {
              developer.log(
                  '[ERROR] _handleWebSocketMessage(): Error parsing new message: ${e.toString()}',
                  name: 'NewMessagingChatScreen',
                  stackTrace: stackTrace);
              developer.log(
                  '[ERROR] Raw message data that failed to parse: ${jsonEncode(messageData)}',
                  name: 'NewMessagingChatScreen');
            }
          }
          break;

        case 'subscribed':
          final chatData = data['chat'];
          if (chatData != null) {
            try {
              final updatedChat = ChatInfo(
                chat_id: chatData['chat_id']?.toString() ?? '',
                chat_icon: chatData['chat_icon']?.toString(),
                chat_name: chatData['chat_name']?.toString() ?? '',
                chat_type: chatData['chat_type']?.toString() ?? 'DIRECT',
                user_id: chatData['user_id']?.toString() ?? '',
                is_subscribed: chatData['is_subscribed'] ?? false,
                unread_count:
                    int.tryParse(chatData['unread_count']?.toString() ?? '0') ??
                        0,
                last_accessed: chatData['last_accessed'] != null
                    ? DateTime.tryParse(chatData['last_accessed'].toString())
                    : null,
                last_read_sequence: int.tryParse(
                        chatData['last_read_sequence']?.toString() ?? '0') ??
                    0,
                entity_type: chatData['entity_type']?.toString() ?? 'USER',
                created_at: DateTime.tryParse(
                        chatData['created_at']?.toString() ?? '') ??
                    DateTime.now(),
                updated_at: DateTime.tryParse(
                        chatData['updated_at']?.toString() ?? '') ??
                    DateTime.now(),
                member_ids: chatData['member_ids'] != null
                    ? (chatData['member_ids'] as List)
                        .map((e) => e.toString())
                        .toList()
                    : null,
                role_type: chatData['role_type']?.toString(),
                is_muted: chatData['is_muted'] ?? false,
                connecting_id: chatData['connecting_id']?.toString(),
              );

              developer.log(
                  '[INFO] _handleWebSocketMessage(): Chat subscription updated',
                  name: 'NewMessagingChatScreen');
              //TODO: Use message_repository.updateChatInBuffer method to update chat info
              setState(() {
                _chatInfo = updatedChat;
              });

              // Now that we're subscribed, load initial messages
              _loadInitialMessages();
            } catch (e, stackTrace) {
              developer.log(
                  '[ERROR] _handleWebSocketMessage(): Error parsing chat data: ${e.toString()}',
                  name: 'NewMessagingChatScreen',
                  stackTrace: stackTrace);
              developer.log(
                  '[ERROR] Raw chat data that failed to parse: ${jsonEncode(chatData)}',
                  name: 'NewMessagingChatScreen');
            }
          }
          break;

        case 'chat_update':
          final chats = data['chats'] as List<dynamic>?;
          if (chats != null) {
            developer.log(
                '[INFO] _handleWebSocketMessage(): Received chat updates',
                name: 'NewMessagingChatScreen',
                error: {
                  'numberOfChats': chats.length,
                  'rawMessage': jsonEncode(data)
                });

            for (final chatData in chats) {
              try {
                developer.log(
                    '[INFO] _handleWebSocketMessage(): Processing chat update',
                    name: 'NewMessagingChatScreen',
                    error: {
                      'chatId': chatData['chat_id']?.toString(),
                      'chatName': chatData['chat_name']?.toString(),
                      'rawChatData': jsonEncode(chatData)
                    });

                // Update chat in buffer using message repository
                await _messageRepository.updateChatInBuffer(chatData);

                // If this update is for the current chat, update the UI state
                if (chatData['chat_id']?.toString() == widget.chat_id) {
                  // Let the repository handle chat parsing and update our UI state
                  final updatedChat = await _messageRepository.bufferManager
                      .getChatInfo(widget.chat_id);
                  if (updatedChat != null) {
                    developer.log(
                        '[INFO] _handleWebSocketMessage(): Updating current chat UI',
                        name: 'NewMessagingChatScreen',
                        error: {
                          'chatId': updatedChat.chat_id,
                          'chatName': updatedChat.chat_name,
                          'unreadCount': updatedChat.unread_count,
                          'lastReadSequence': updatedChat.last_read_sequence
                        });
                    setState(() {
                      _chatInfo = updatedChat;
                    });
                  }
                }
              } catch (e, stackTrace) {
                developer.log(
                    '[ERROR] _handleWebSocketMessage(): Error handling chat update: ${e.toString()}',
                    name: 'NewMessagingChatScreen',
                    error: {
                      'chatId': chatData['chat_id']?.toString(),
                      'error': e.toString(),
                      'rawChatData': jsonEncode(chatData)
                    },
                    stackTrace: stackTrace);
              }
            }
          } else {
            developer.log(
                '[WARNING] _handleWebSocketMessage(): Received chat_update without chats array',
                name: 'NewMessagingChatScreen',
                error: {'rawMessage': jsonEncode(data)});
          }
          break;

        case 'marked_messages_as_read':
          // Update local chat info with new last read sequence
          if (data['chat_id'] == widget.chat_id &&
              data['user_id'] != _currentUserId) {
            final int newSequence = data['last_read_sequence'] ?? 0;
            if (_chatInfo != null) {
              setState(() {
                _chatInfo!.last_read_sequence = newSequence;
              });
            }
          }
          break;

        default:
          developer.log(
              '[WARNING] _handleWebSocketMessage(): Unknown message type: ${data['type']}',
              name: 'NewMessagingChatScreen');
      }

      developer.log(
          '[EXIT] _handleWebSocketMessage(): Message processed successfully',
          name: 'NewMessagingChatScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _handleWebSocketMessage(): ${e.toString()}',
          name: 'NewMessagingChatScreen', stackTrace: stackTrace);
      developer.log('[ERROR] Raw message that failed to parse: $message',
          name: 'NewMessagingChatScreen');
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  // Pick files using FilePicker
  Future<void> _pickFiles() async {
    try {
      if (_selectedFiles.length >= _maxFiles) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Maximum $_maxFiles files allowed')),
        );
        return;
      }

      // Open file picker
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
      );

      if (result != null) {
        setState(() {
          // Add new files, respecting the max limit
          final availableSlots = _maxFiles - _selectedFiles.length;
          final filesToAdd = result.files.take(availableSlots).toList();
          _selectedFiles.addAll(filesToAdd);

          if (filesToAdd.length < result.files.length) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content: Text(
                      'Only added ${filesToAdd.length} files due to the limit of $_maxFiles')),
            );
          }
        });
      }
    } catch (e) {
      developer.log('[ERROR] _pickFiles(): ${e.toString()}',
          name: 'NewMessagingChatScreen');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking files: ${e.toString()}')),
      );
    }
  }

  // Upload a file to the server
  Future<Map<String, dynamic>> _uploadFile(PlatformFile file) async {
    setState(() {
      _uploadingFiles[file.path!] = true;
    });

    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(AppConstants.newMessaging_uploadFiles),
      );

      // Add authorization header
      request.headers['Authorization'] =
          'Bearer ${AppConstants.appData.newMessagingToken}';

      // Add file to the request
      request.files.add(await http.MultipartFile.fromPath('files', file.path!));

      // Send the request
      final response = await request.send();
      final responseData = await http.Response.fromStream(response);
      final jsonResponse = json.decode(responseData.body);

      if (jsonResponse['success']) {
        // Get the file data from the response
        final fileData = jsonResponse['data'][0];
        return {
          'url': fileData['url'] ?? '',
          'originalName': file.name,
          'size': file.size,
          'mimetype': fileData['mimetype'] ?? '',
          'filename': fileData['file_name'] ?? '',
          'type': fileData['file_type'] ?? '',
        };
      }

      throw Exception('Failed to upload file: ${file.name}');
    } catch (e) {
      developer.log('[ERROR] _uploadFile(): ${e.toString()}',
          name: 'NewMessagingChatScreen');
      throw e;
    } finally {
      setState(() {
        _uploadingFiles[file.path!] = false;
      });
    }
  }

  // Get file preview widget based on file type
  Widget _getFilePreview(PlatformFile file) {
    final extension = file.extension?.toLowerCase() ?? '';

    // For image files, show a thumbnail
    if (['jpg', 'jpeg', 'png', 'gif'].contains(extension)) {
      return Image.file(
        File(file.path!),
        fit: BoxFit.cover,
        width: 60,
        height: 60,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(Icons.image, size: 30);
        },
      );
    }

    // For other file types, show an appropriate icon
    IconData iconData;
    switch (extension) {
      case 'pdf':
        iconData = Icons.picture_as_pdf;
        break;
      case 'doc':
      case 'docx':
        iconData = Icons.description;
        break;
      case 'xls':
      case 'xlsx':
        iconData = Icons.table_chart;
        break;
      case 'mp4':
      case 'mov':
      case 'avi':
        iconData = Icons.video_file;
        break;
      case 'mp3':
      case 'wav':
      case 'ogg':
        iconData = Icons.audio_file;
        break;
      default:
        iconData = Icons.insert_drive_file;
    }

    return Icon(iconData, size: 30);
  }

  Future<void> _sendMessage(String text) async {
    try {
      text = text.trim();
      if (text.isEmpty && _selectedFiles.isEmpty) {
        return;
      }
      developer.log(
          '[log] [new_messaging_chat_screen:_sendMessage] Checking Preconditions & Creating if not present before sending message - connection, chat presence, subscription',
          name: 'NewMessagingChatScreen');

      // Checking pre-conditions
      await _connectWebSocket();
      if (_chatInfo == null || _chatInfo!.chat_id == "") {
        await _createTheChat();
      }
      if (_chatInfo!.is_subscribed == false ||
          _chatInfo!.is_subscribed == null) {
        await _subscribeToChat();
      }

      developer.log(
          '[ChatScreen] Sending message from chat ${_chatInfo?.chat_id}',
          name: 'NewMessagingChatScreen');

      // Create temporary message ID
      final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';

      // Upload files if any
      List<Map<String, dynamic>> attachments = [];
      if (_selectedFiles.isNotEmpty) {
        for (var file in _selectedFiles) {
          try {
            final fileData = await _uploadFile(file);
            attachments.add(fileData);
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content:
                      Text('Failed to upload ${file.name}: ${e.toString()}')),
            );
          }
        }
      }

      // Create temporary message for immediate display
      final tempMessage = Message(
        message_id: tempId,
        chat_id: _chatInfo!.chat_id,
        sender_id: _currentUserId,
        content: text,
        message_type: attachments.isNotEmpty
            ? (attachments[0]['mimetype']?.toString().startsWith('image/') ==
                    true
                ? 'IMAGE'
                : 'FILE')
            : 'TEXT',
        metadata: {'tempId': tempId},
        attachments: attachments,
        sequence_number:
            _messages.isEmpty ? 1 : _messages.last.sequence_number + 1,
        created_at: DateTime.now(),
        updated_at: DateTime.now(),
      );

      // Add temporary message to state
      _messageStateManager.handleNewMessage(tempMessage);

      // Send message via websocket
      final message = {
        'type': 'new_message',
        'chat_id': _chatInfo!.chat_id,
        'content': text,
        'message_type': attachments.isNotEmpty
            ? (attachments[0]['mimetype']?.toString().startsWith('image/') ==
                    true
                ? 'image'
                : 'file')
            : 'text',
        'metadata': {'tempId': tempId},
        'attachments': attachments,
      };

      final messageJson = jsonEncode(message);
      developer.log('[ChatScreen] Preparing to send message: $messageJson',
          name: 'NewMessagingChatScreen');

      try {
        if (_channel == null) {
          developer.log(
              '[ERROR] WebSocket channel is null. Cannot send message.',
              name: 'NewMessagingChatScreen');
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('Connection error. Please try again.')));
          return;
        }

        _channel!.sink.add(messageJson);

        developer.log('[SUCCESS] Message sent via WebSocket',
            name: 'NewMessagingChatScreen');
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] Failed to send message via WebSocket: ${e.toString()}',
            name: 'NewMessagingChatScreen',
            stackTrace: stackTrace);
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to send message: ${e.toString()}')));
      }

      _messageController.clear();
      setState(() {
        _selectedFiles.clear();
      });
      _scrollToBottom();
    } catch (e, stackTrace) {
      developer.log('[ERROR] _sendMessage(): ${e.toString()}',
          name: 'NewMessagingChatScreen', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Formats message timestamp for display
  /// Returns time for today's messages, 'Yesterday' for yesterday's messages,
  /// and date for older messages
  String _formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      // Today, show time only
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      // Other days, show date
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  /// Builds the message list with proper ordering and styling
  Widget _buildMessageList() {
    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      reverse: false, // Show newest messages at bottom
      itemCount: _messages.length,
      separatorBuilder: (_, __) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isMe = message.sender_id == _currentUserId;
        final timestamp = _formatMessageTime(message.created_at);

        return _MessageBubble(
          message: message.content,
          isMe: isMe,
          timestamp: timestamp,
          sequenceNumber: message.sequence_number,
          attachments: message.attachments,
          object: message.object,
        );
      },
    );
  }

  /// Shows chat options menu based on user role
  void _showChatOptionsMenu(BuildContext context) {
    if (_chatInfo == null) return;

    final userRole = _chatInfo!.role_type;
    final isAdmin = userRole == 'ADMIN';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 20,
            right: 20,
            top: 20,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // Title
              Text(
                'Chat Options',
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 20),

              if (isAdmin) ...[
                // Admin options
                _buildAdminOptions(context),
              ] else ...[
                // Member/null role options
                _buildMemberOptions(context),
              ],

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds options for admin users
  Widget _buildAdminOptions(BuildContext context) {
    return Column(
      children: [
        // Edit Chat Name
        _buildEditChatNameOption(context),
        const SizedBox(height: 15),

        // Message Access Control
        _buildMessageAccessOptions(context),
        const SizedBox(height: 15),

        // Mute with warning
        _buildAdminMuteOption(context),
      ],
    );
  }

  /// Builds options for member/null role users
  Widget _buildMemberOptions(BuildContext context) {
    return Column(
      children: [
        // Mute option
        // _buildMuteOption(context),
        // const SizedBox(height: 15),

        // Report option
        _buildReportOption(context),
      ],
    );
  }

  /// Builds edit chat name option for admins
  Widget _buildEditChatNameOption(BuildContext context) {
    // Update the controller text when the chat info changes
    if (_chatInfo != null) {
      _nameController.text = widget.chat_name ?? '';
      _nameController.selection = TextSelection.fromPosition(
        TextPosition(offset: _nameController.text.length),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chat Name',
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 15),
            AppTextFields.allTextField(
              context: context,
              textEditingController: _nameController,
              hintText: 'Enter chat name',
              maxEntry: 35,
            ),

            const SizedBox(height: 15),
            StoreCommonWidgets.storeButtons(
              buttonColor: AppColors.brandBlack,
              textAndIcon: Text(
                'Save',
                style: AppTextStyle.access0(textColor: AppColors.appWhite),
              ),
              onTapButton: () => _updateChatName(context, _nameController.text),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds message access control options for admins
  Widget _buildMessageAccessOptions(BuildContext context) {
    String currentAccess = _chatInfo!.message_access ?? 'ALL';
    String selectedAccess = currentAccess; // Track selected option locally

    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 5),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            border: Border.all(color: AppColors.borderColor1),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Who can Message',
                  style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                ),
                const SizedBox(height: 15),

                // Admin Only
                InkWell(
                  onTap: () {
                    setState(() {
                      selectedAccess = 'ADMIN_ONLY';
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Checkbox(
                          value: selectedAccess == 'ADMIN_ONLY',
                          activeColor: AppColors.brandBlack,
                          onChanged: (value) {
                            setState(() {
                              selectedAccess = value == true ? 'ADMIN_ONLY' : selectedAccess;
                            });
                          },
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Admin Only',
                                style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Only admins can send messages',
                                style: AppTextStyle.contentText0(
                                  textColor: AppColors.appBlack,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Members Only - Disabled
                Opacity(
                  opacity: 0.5, // Visual indication of disabled state
                  child: AbsorbPointer(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Checkbox(
                            value: selectedAccess == 'MEMBERS_ONLY',
                            activeColor: AppColors.brandBlack,
                            onChanged: null, // Disable interaction
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Admin and Members (coming soon)',
                                  style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Only members and admins can send messages',
                                  style: AppTextStyle.contentText0(
                                    textColor: AppColors.appBlack,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // All - Disabled
                Opacity(
                  opacity: 0.5, // Visual indication of disabled state
                  child: AbsorbPointer(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Checkbox(
                            value: selectedAccess == 'ALL',
                            activeColor: AppColors.brandBlack,
                            onChanged: null, // Disable interaction
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'All (coming soon)',
                                  style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Everyone can send messages',
                                  style: AppTextStyle.contentText0(
                                    textColor: AppColors.appBlack,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 15),
                StoreCommonWidgets.storeButtons(
                  buttonColor: AppColors.brandBlack,
                  textAndIcon: Text(
                    'Save',
                    style: AppTextStyle.access0(textColor: AppColors.appWhite),
                  ),
                  onTapButton: () => _updateMessageAccess(context, selectedAccess),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Builds mute option for regular users
  Widget _buildMuteOption(BuildContext context) {
    final isMuted = _chatInfo!.is_muted;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: InkWell(
        onTap: () => _toggleMute(context),
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            children: [
              Icon(
                isMuted ? Icons.volume_off : Icons.volume_up,
                color: AppColors.appBlack,
                size: 24,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isMuted ? 'Unmute Chat' : 'Mute Chat',
                      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      isMuted ? 'Turn on notifications' : 'Turn off notifications',
                      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds mute option for admin users with warning
  Widget _buildAdminMuteOption(BuildContext context) {
    final isMuted = _chatInfo!.is_muted;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: InkWell(
        onTap: () => _showAdminMuteWarning(context),
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            children: [
              Icon(
                isMuted ? Icons.volume_off : Icons.volume_up,
                color: AppColors.appBlack,
                size: 24,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isMuted ? 'Unmute Chat' : 'Mute Chat',
                      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      isMuted ? 'Turn on notifications' : 'Turn off notifications',
                      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds report option for regular users
  Widget _buildReportOption(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: InkWell(
        onTap: () => _reportChat(context),
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            children: [
              const Icon(
                Icons.report,
                color: Colors.red,
                size: 24,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Report Chat',
                      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Report inappropriate content',
                      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Updates chat name
  Future<void> _updateChatName(BuildContext context, String newName) async {
    if (newName.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Chat name cannot be empty')),
      );
      return;
    }

    try {
      await _messageRepository.updateChatName(
        chatId: _chatInfo!.chat_id,
        chatName: newName.trim(),
      );

      setState(() {
        _chatInfo!.chat_name = newName.trim();
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Chat name updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update chat name: ${e.toString()}')),
      );
    }
  }

  /// Updates message access control
  Future<void> _updateMessageAccess(
      BuildContext context, String messageAccess) async {
    try {
      await _messageRepository.updateChatMessageAccess(
        chatId: _chatInfo!.chat_id,
        messageAccess: messageAccess,
      );

      setState(() {
        _chatInfo!.message_access = messageAccess;
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message access updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to update message access: ${e.toString()}')),
      );
    }
  }

  /// Toggles mute status
  Future<void> _toggleMute(BuildContext context) async {
    try {
      final newMuteStatus = !_chatInfo!.is_muted;

      await _messageRepository.updateMuteStatus(
        chatId: _chatInfo!.chat_id,
        isMuted: newMuteStatus,
      );

      setState(() {
        _chatInfo!.is_muted = newMuteStatus;
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(newMuteStatus ? 'Chat muted' : 'Chat unmuted'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to update mute status: ${e.toString()}')),
      );
    }
  }

  /// Toggles mute status without navigation (for use after dialog)
  Future<void> _toggleMuteWithoutNavigation() async {
    try {
      final newMuteStatus = !_chatInfo!.is_muted;

      await _messageRepository.updateMuteStatus(
        chatId: _chatInfo!.chat_id,
        isMuted: newMuteStatus,
      );

      setState(() {
        _chatInfo!.is_muted = newMuteStatus;
      });

      // Close the bottom sheet by finding the correct context
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(newMuteStatus ? 'Chat muted' : 'Chat unmuted'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to update mute status: ${e.toString()}')),
      );
    }
  }

  /// Shows warning dialog for admin mute
  void _showAdminMuteWarning(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Admin Mute Warning'),
        content: const Text(
          'As an admin, muting this group chat is not recommended as you may miss important messages and member activities. Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext); // Close dialog
              _toggleMuteWithoutNavigation(); // Don't try to close bottom sheet from here
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Reports the chat
  void _reportChat(BuildContext context) {
    Navigator.pop(context); // Close the options menu

    // Determine if reporting a store or user based on entity_type
    final isStore = widget.entity_type == 'STORE';
    final isUser = widget.entity_type == 'USER';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReportScreen(
          reference: widget.connecting_id.isNotEmpty
              ? widget.connecting_id
              : widget.chat_id,
          isStore: isStore,
          isUser: isUser,
        ),
      ),
    );
  }

  /// Checks if user can send messages based on message access and role
  bool _canSendMessages() {
    if (_chatInfo == null) return true; // Allow for new chats

    final messageAccess = _chatInfo!.message_access;
    final userRole = _chatInfo!.role_type;

    // If no message access restriction, allow all
    if (messageAccess == null || messageAccess == 'ALL') {
      return true;
    }

    // If ADMIN_ONLY, only admins can send
    if (messageAccess == 'ADMIN_ONLY') {
      return userRole == 'ADMIN';
    }

    // If MEMBERS_ONLY, members and admins can send (null role means non-member)
    if (messageAccess == 'MEMBERS_ONLY') {
      return userRole != null; // Any role (ADMIN, MEMBER) can send
    }

    return true; // Default allow
  }

  /// Gets the restriction message to display
  String _getRestrictionMessage() {
    if (_chatInfo == null) return '';

    final messageAccess = _chatInfo!.message_access;
    final userRole = _chatInfo!.role_type;

    if (messageAccess == 'ADMIN_ONLY' && userRole != 'ADMIN') {
      return 'Only admins can send messages in this chat';
    }

    if (messageAccess == 'MEMBERS_ONLY' && userRole == null) {
      return 'Only members can send messages in this chat';
    }

    return '';
  }

  /// Builds the message input area or restriction message
  Widget _buildMessageInputArea() {
    if (!_canSendMessages()) {
      // Show restriction message
      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.textFieldFill2,
          borderRadius: BorderRadius.circular(12),
          // border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _getRestrictionMessage(),
                style: AppTextStyle.smallTextRegular(
                  textColor: AppColors.writingBlack1,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Show normal message input
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // IconButton(
          //   padding: const EdgeInsets.all(8),
          //   constraints: const BoxConstraints(
          //     minWidth: 40,
          //     minHeight: 46,
          //   ),
          //   icon: Icon(
          //     _showEmoji ? Icons.emoji_emotions : Icons.emoji_emotions_outlined,
          //     color: AppColors.writingColor2,
          //     size: 24,
          //   ),
          //   onPressed: () {
          //     setState(() => _showEmoji = !_showEmoji);
          //   },
          // ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(18),
                // border: Border.all(color: AppColors.borderColor1, width: 1.5),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                          IconButton(
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 40,
                      minHeight: 46,
                    ),
                    icon: Icon(
                      _showEmoji ? Icons.emoji_emotions : Icons.emoji_emotions_outlined,
                      color: AppColors.writingColor2,
                      size: 24,
                    ),
                    onPressed: () {
                      setState(() => _showEmoji = !_showEmoji);
                    },
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Selected files display
                        if (_selectedFiles.isNotEmpty)
                          Container(
                            height: 70,
                            margin: const EdgeInsets.only(
                                bottom: 8, top: 8, left: 8),
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: _selectedFiles.length,
                              itemBuilder: (context, index) {
                                final file = _selectedFiles[index];
                                final isUploading =
                                    _uploadingFiles[file.path!] ?? false;

                                return Container(
                                  width: 60,
                                  margin: const EdgeInsets.only(right: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Stack(
                                    children: [
                                      Center(
                                        child: _getFilePreview(file),
                                      ),
                                      if (isUploading)
                                        Center(
                                          child: AppCommonWidgets.appCircularProgress(),
                                        ),
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              _selectedFiles.removeAt(index);
                                            });
                                          },
                                          child: Container(
                                            decoration: const BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle,
                                            ),
                                            padding: const EdgeInsets.all(2),
                                            child: const Icon(
                                              Icons.close,
                                              size: 12,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        TextField(
                          controller: _messageController,
                          maxLines: 5,
                          minLines: 1,
                          keyboardType: TextInputType.multiline,
                          textInputAction: TextInputAction.newline,
                          onChanged: (value) {
                            setState(() {}); // Trigger rebuild to update icons
                          },
                          style: AppTextStyle.contentTextMessaging(
                            textColor: AppColors.appBlack,
                            isLineHeightEnable: false,
                          ),
                          decoration: const InputDecoration(
                            hintText: 'Type a message...',
                            hintStyle:
                                TextStyle(color: AppColors.writingColor2),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 10),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_messageController.text.isNotEmpty ||
                      _selectedFiles.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(left: 4, right: 4),
                      decoration: BoxDecoration(
                        color: AppColors.appBlack,
                        borderRadius: BorderRadius.circular(24),
                        // border: Border.all(color: AppColors.appBlack, width: 1.5),
                      ),
                      child: IconButton(
                        padding: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(
                          minWidth: 40,
                          minHeight: 40,
                        ),
                        icon: const Icon(
                          Icons.send,
                          color: AppColors.appWhite,
                          size: 24,
                        ),
                        onPressed: () => _sendMessage(_messageController.text),
                      ),
                    ),
                ],
              ),
            ),
          ),
          IconButton(
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 46,
            ),
            icon: const Icon(
              Icons.attach_file,
              color: AppColors.writingColor2,
              size: 24,
            ),
            onPressed: _pickFiles,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Explicitly hide the bottom navigation bar
    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context);
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: widget.chat_name.startsWith('SAI_')
              ? AppColors.brandBlack
              : AppColors.appWhite,
          titleSpacing: 0,
          elevation: 1,
          leading: IconButton(
            icon: Icon(Icons.arrow_back,
                color: widget.chat_name.startsWith('SAI_')
                    ? AppColors.appWhite
                    : AppColors.appBlack),
            onPressed: () => Navigator.pop(context),
          ),
          title: Row(
            children: [
              CustomImageContainer(
                width: 40,
                height: 40,
                imageUrl: widget.chat_icon.isNotEmpty ? widget.chat_icon : null,
                imageType: widget.entity_type == 'STORE'
                    ? CustomImageContainerType.store
                    : CustomImageContainerType.user,
                // Show custom badge for GROUP chats, level badge for others
                showCustomBadge: _chatInfo?.chat_type == 'GROUP',
                customBadgeSvgPath: AppImages.storeMessagingGroupLabel,
                // showLevelBadge: _chatInfo?.chat_type != 'GROUP',
                // level: '1',
                // userBadgeBorderWidth: 1,
                // storeBadgeBorderWidth: 1.2,
                // userBadgeFontSize: 7,
                // storeBadgeFontSize: 7,
                // Default level for chat screen
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.chat_name,
                      style: AppTextStyle.access0(
                          textColor: widget.chat_name.startsWith('SAI_')
                              ? AppColors.appWhite
                              : AppColors.appBlack),
                    ),
                    Text(
                      widget.last_seen,
                      style: AppTextStyle.smallText(
                        textColor: widget.chat_name.startsWith('SAI_')
                            ? AppColors.appWhite.withOpacity(0.8)
                            : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.more_vert,
                  color: widget.chat_name.startsWith('SAI_')
                      ? AppColors.appWhite
                      : AppColors.appBlack),
              onPressed: () => _showChatOptionsMenu(context),
            ),
          ],
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: _isLoading
                    ? Center(child: AppCommonWidgets.appCircularProgress())
                    : _buildMessageList(),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, -1),
                      spreadRadius: 1,
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Use the new message input area method
                    _buildMessageInputArea(),
                    if (_showEmoji && _canSendMessages())
                      SizedBox(
                        height: 250,
                        child: EmojiPicker(
                          onEmojiSelected: (category, emoji) {
                            _messageController.text += emoji.emoji;
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        resizeToAvoidBottomInset: true,
      ),
    );
  }
}

/// Manages message state and operations
class _MessageStateManager {
  List<dynamic> messages;
  final Function(List<dynamic>) onMessagesChanged;
  final VoidCallback onScrollToBottom;
  final String currentUserId;

  _MessageStateManager({
    required this.messages,
    required this.onMessagesChanged,
    required this.onScrollToBottom,
    required this.currentUserId,
  });

  /// Updates or adds a new message with proper state management
  void handleNewMessage(Message newMessage) {
    try {
      // Log incoming message details
      developer.log('[ENTER] handleNewMessage(): Processing new message',
          name: 'MessageStateManager',
          error: {
            'messageId': newMessage.message_id,
            'messageMetadata': jsonEncode(newMessage.metadata),
            'senderId': newMessage.sender_id,
            'sequenceNumber': newMessage.sequence_number,
            'currentMessageCount': messages.length
          });

      // First check for exact message ID match to prevent duplicates
      // Only check non-temporary messages
      final isDuplicate = messages.any((m) {
        final isMatch = m.message_id == newMessage.message_id;
        if (isMatch) {
          developer.log('[DEBUG] handleNewMessage(): Found potential duplicate',
              name: 'MessageStateManager',
              error: {
                'existingMessageId': m.message_id,
                'newMessageId': newMessage.message_id,
                'existingMetadata': jsonEncode(m.metadata),
                'newMetadata': jsonEncode(newMessage.metadata)
              });
        }
        return isMatch && !m.message_id.startsWith('temp_');
      });

      if (isDuplicate) {
        developer.log(
            '[INFO] handleNewMessage(): Non-temp message already exists, skipping',
            name: 'MessageStateManager',
            error: {
              'messageId': newMessage.message_id,
              'metadata': jsonEncode(newMessage.metadata)
            });
        return;
      }

      // Get tempId from message metadata
      final messageTempId = newMessage.metadata?['tempId']?.toString();

      // Check for temporary message match based on tempId in metadata
      final tempIndex = messages.indexWhere((m) {
        // Only match tempId if the current message is from the same sender
        if (m.sender_id != newMessage.sender_id) {
          return false;
        }

        // Get tempId from metadata
        final existingTempId = m.metadata?['tempId']?.toString();

        // Log comparison for debugging
        developer.log('[DEBUG] handleNewMessage(): Comparing tempIds',
            name: 'MessageStateManager',
            error: {
              'existingMessageId': m.message_id,
              'existingTempId': existingTempId,
              'existingMetadata': jsonEncode(m.metadata),
              'newMessageId': newMessage.message_id,
              'newTempId': messageTempId,
              'newMetadata': jsonEncode(newMessage.metadata)
            });

        // Match if both have tempId and they are equal
        return existingTempId != null &&
            messageTempId != null &&
            existingTempId == messageTempId;
      });

      if (tempIndex != -1) {
        final existingMessage = messages[tempIndex];
        developer.log('[INFO] handleNewMessage(): Found matching temp message',
            name: 'MessageStateManager',
            error: {
              'existingMessageId': existingMessage.message_id,
              'existingTempId': existingMessage.metadata?['tempId'],
              'existingSequence': existingMessage.sequence_number,
              'newMessageId': newMessage.message_id,
              'newTempId': messageTempId,
              'newSequence': newMessage.sequence_number
            });

        // Only replace temp message with real message (sequence > 0)
        if (newMessage.sequence_number > 0) {
          messages.removeAt(tempIndex);
          messages.add(newMessage);
          messages
              .sort((a, b) => a.sequence_number.compareTo(b.sequence_number));
          onMessagesChanged(List<dynamic>.from(messages));

          developer.log(
              '[INFO] handleNewMessage(): Replaced temp message with real message',
              name: 'MessageStateManager',
              error: {
                'messageId': newMessage.message_id,
                'tempId': messageTempId,
                'sequenceNumber': newMessage.sequence_number,
                'finalMessageCount': messages.length
              });
        } else {
          developer.log(
              '[INFO] handleNewMessage(): Skipping replacement - new message has no sequence number',
              name: 'MessageStateManager',
              error: {
                'messageId': newMessage.message_id,
                'tempId': messageTempId,
                'sequenceNumber': newMessage.sequence_number
              });
        }
      } else {
        // No temp message found, just add the new one
        messages.add(newMessage);
        messages.sort((a, b) => a.sequence_number.compareTo(b.sequence_number));
        onMessagesChanged(List<dynamic>.from(messages));

        developer.log('[INFO] handleNewMessage(): Added new message',
            name: 'MessageStateManager',
            error: {
              'messageId': newMessage.message_id,
              'tempId': messageTempId,
              'sequenceNumber': newMessage.sequence_number,
              'finalMessageCount': messages.length
            });
      }

      // Log final message list state
      developer.log('[EXIT] handleNewMessage(): Message processed successfully',
          name: 'MessageStateManager',
          error: {
            'finalMessageCount': messages.length,
            'messageIds': messages
                .map((m) => '${m.message_id}(seq:${m.sequence_number})')
                .toList(),
            'tempIds': messages.map((m) => m.metadata?['tempId']).toList()
          });
    } catch (e, stackTrace) {
      developer.log('[ERROR] handleNewMessage(): Failed to process message',
          name: 'MessageStateManager',
          error: {
            'error': e.toString(),
            'messageId': newMessage.message_id,
            'metadata': jsonEncode(newMessage.metadata)
          },
          stackTrace: stackTrace);
    }
  }

  /// Loads initial messages with proper ordering
  void setInitialMessages(List<dynamic> newMessages) {
    try {
      developer.log('[ENTER] setInitialMessages(): Setting initial messages',
          name: 'MessageStateManager',
          error: {
            'messageCount': newMessages.length,
          });

      // Clear existing messages
      messages.clear();

      // Add new messages, ensuring no duplicates
      final uniqueMessages = <String>{};
      for (final message in newMessages) {
        if (!uniqueMessages.contains(message.message_id)) {
          messages.add(message);
          uniqueMessages.add(message.message_id);
        }
      }

      // Sort by sequence number
      messages.sort((a, b) => a.sequence_number.compareTo(b.sequence_number));

      // Update UI without scrolling
      onMessagesChanged(List<dynamic>.from(messages));

      developer.log(
          '[EXIT] setInitialMessages(): Initial messages set successfully',
          name: 'MessageStateManager',
          error: {
            'finalMessageCount': messages.length,
          });
    } catch (e, stackTrace) {
      developer.log('[ERROR] setInitialMessages(): ${e.toString()}',
          name: 'MessageStateManager', stackTrace: stackTrace);
    }
  }

  /// Adds more messages to the list
  void addMoreMessages(List<dynamic> newMessages, {required bool older}) {
    try {
      developer.log('[ENTER] addMoreMessages(): Adding more messages',
          name: 'MessageStateManager',
          error: {
            'newMessageCount': newMessages.length,
            'currentMessageCount': messages.length,
            'loadingOlder': older,
          });

      // Add new messages, ensuring no duplicates
      final uniqueMessages = <String>{};
      messages.forEach((m) => uniqueMessages.add(m.message_id));

      for (final message in newMessages) {
        if (!uniqueMessages.contains(message.message_id)) {
          messages.add(message);
          uniqueMessages.add(message.message_id);
        }
      }

      // Sort by sequence number
      messages.sort((a, b) => a.sequence_number.compareTo(b.sequence_number));

      // Update UI without scrolling
      onMessagesChanged(List<dynamic>.from(messages));

      developer.log('[EXIT] addMoreMessages(): Messages added successfully',
          name: 'MessageStateManager',
          error: {
            'finalMessageCount': messages.length,
          });
    } catch (e, stackTrace) {
      developer.log('[ERROR] addMoreMessages(): ${e.toString()}',
          name: 'MessageStateManager', stackTrace: stackTrace);
    }
  }
}

/// Widget to display a single message bubble
class _MessageBubble extends StatefulWidget {
  final String message;
  final bool isMe;
  final String timestamp;
  final int sequenceNumber;
  final List<dynamic>? attachments;
  final String? object; // Object reference for previews

  const _MessageBubble({
    required this.message,
    required this.isMe,
    required this.timestamp,
    required this.sequenceNumber,
    this.attachments,
    this.object,
  });

  @override
  State<_MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<_MessageBubble> {
  bool _hasUrl(String text) {
    final urlRegex = RegExp(
      r'(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
      caseSensitive: false,
    );
    return urlRegex.hasMatch(text);
  }

  // Removed unused _extractUrl method

  // Object preview widget
  Widget? _objectPreviewWidget;
  bool _isLoadingPreview = false;

  @override
  void initState() {
    super.initState();
    _loadObjectPreview();
  }

  Future<void> _loadObjectPreview() async {
    if (widget.object == null || widget.object!.isEmpty) return;

    setState(() {
      _isLoadingPreview = true;
    });

    try {
      // Use the ObjectPreviewManager to get the preview widget
      final previewManager = ObjectPreviewManager();
      final previewWidget = await previewManager.getObjectPreviewWidget(
        widget.object!,
        widget.isMe,
      );

      if (mounted) {
        setState(() {
          _objectPreviewWidget = previewWidget;
          _isLoadingPreview = false;
        });
      }
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] _loadObjectPreview(): Failed to load object preview: ${e.toString()}',
          name: 'MessageBubble',
          stackTrace: stackTrace);

      if (mounted) {
        setState(() {
          _isLoadingPreview = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Extract URLs from the message
    final urlsFound = hasUrl(widget.message);
    final urls = urlsFound ? extractAllUrls(widget.message) : <String>[];

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Align(
        alignment: widget.isMe ? Alignment.centerRight : Alignment.centerLeft,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * .75,
          ),
          child: Column(
            crossAxisAlignment:
                widget.isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              // Display file attachments if any
              if (widget.attachments != null && widget.attachments!.isNotEmpty)
                ...widget.attachments!.map((attachment) =>
                    buildAttachmentPreview(attachment, widget.isMe, context)),

              // Display object preview if available
              if (_isLoadingPreview)
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: widget.isMe
                        ? AppColors.appRichBlack.withOpacity(0.9)
                        : AppColors.textFieldFill2,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: AppCommonWidgets.appCircularProgress(),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Loading preview...',
                        style: TextStyle(
                          fontSize: 14,
                          color:
                              widget.isMe ? Colors.white70 : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                )
              else if (_objectPreviewWidget != null)
                _objectPreviewWidget!,

              // Display URL previews if any
              if (urls.isNotEmpty)
                MultiLinkPreview(urls: urls, isMe: widget.isMe),

              // Display message text if not empty
              if (widget.message.isNotEmpty)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        widget.isMe
                            ? AppColors.appRichBlack
                            : AppColors.textFieldFill2,
                        widget.isMe
                            ? AppColors.appRichBlack
                            : AppColors.textFieldFill2,
                        // isMe ? const Color.fromARGB(255, 0, 100, 40) : const Color.fromARGB(255, 159, 222, 184),
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(20),
                      topRight: const Radius.circular(20),
                      bottomLeft: widget.isMe
                          ? const Radius.circular(20)
                          : const Radius.circular(0),
                      bottomRight: widget.isMe
                          ? const Radius.circular(0)
                          : const Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: widget.isMe
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildMessageText(
                        widget.message,
                        widget.isMe
                            ? AppTextStyle.contentTextMessaging(
                                textColor: Colors.white)
                            : AppTextStyle.contentTextMessaging(
                                textColor: Colors.black),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageText(String text, TextStyle baseStyle) {
    if (!_hasUrl(text)) {
      return SelectableText(text,
          style: widget.isMe
              ? AppTextStyle.contentTextMessaging(textColor: AppColors.appWhite)
              : AppTextStyle.contentTextMessaging(
                  textColor: AppColors.appBlack));
    }

    final urlRegex = RegExp(
      r'(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
      caseSensitive: false,
    );

    final spans = <InlineSpan>[];
    int lastIndex = 0;

    for (final match in urlRegex.allMatches(text)) {
      // Add text before the link
      if (match.start > lastIndex) {
        spans.add(TextSpan(
          text: text.substring(lastIndex, match.start),
          style: baseStyle,
        ));
      }

      // Add the link
      String url = match.group(0)!;
      if (!url.startsWith('http')) {
        url = 'https://$url';
      }

      spans.add(
        TextSpan(
          text: match.group(0),
          style: AppTextStyle.access0(
            textColor: AppColors.brandBlack,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () async {
              final uri = Uri.parse(url);
              if (await canLaunchUrl(uri)) {
                await launchUrl(uri, mode: LaunchMode.externalApplication);
              }
            },
        ),
      );

      lastIndex = match.end;
    }

    // Add remaining text after the last link
    if (lastIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastIndex),
        style: baseStyle,
      ));
    }

    return SelectableText.rich(TextSpan(children: spans),
        style: widget.isMe
            ? AppTextStyle.contentTextMessaging(textColor: AppColors.appWhite)
            : AppTextStyle.contentTextMessaging(textColor: AppColors.appBlack));
  }
}
