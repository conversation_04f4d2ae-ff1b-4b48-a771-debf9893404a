import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/support/app_suggestions/app_suggestions_bloc.dart';
import 'package:swadesic/features/support/support_common_widgets.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AppSuggestionsScreen extends StatefulWidget {
  final String entityReference;
  
  const AppSuggestionsScreen({
    Key? key,
    required this.entityReference,
  }) : super(key: key);

  @override
  AppSuggestionsScreenState createState() => AppSuggestionsScreenState();
}

class AppSuggestionsScreenState extends State<AppSuggestionsScreen>
    with AutomaticKeepAliveClientMixin<AppSuggestionsScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  late AppSuggestionsBloc appSuggestionsBloc;

  @override
  void initState() {
    super.initState();
    appSuggestionsBloc = AppSuggestionsBloc(context, widget.entityReference);
    appSuggestionsBloc.init();
  }

  //region Dispose
  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    imageCache.clear();
    appSuggestionsBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: StreamBuilder<AppSuggestionsState>(
        stream: appSuggestionsBloc.appSuggestionsCtrl.stream,
        builder: (context, snapshot) {
          return GestureDetector(
            onTap: () {
              CommonMethods.closeKeyboard(context);
            },
            child: body(),
          );
        },
      ),
    );
  }

  // endregion

  Widget body() {
    return StreamBuilder<AppSuggestionsState>(
      stream: appSuggestionsBloc.appSuggestionsCtrl.stream,
      initialData: AppSuggestionsState.Loading,
      builder: (context, snapshot) {
        // Loading
        if (snapshot.data == AppSuggestionsState.Loading) {
          return AppCommonWidgets.appCircularProgress();
        }
        
        // Success
        if (snapshot.data == AppSuggestionsState.Success) {
          return Column(
            children: [
              searchField(),
              appSuggestionsBloc.finalFilteredSuggestionsList.isEmpty
                  ? Expanded(
                      child: RefreshIndicator(
                        color: AppColors.brandBlack,
                        onRefresh: () async {
                          await appSuggestionsBloc.init();
                        },
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Container(
                            alignment: Alignment.center,
                            height: MediaQuery.of(context).size.width,
                            child: NoResult(message: "No app suggestions yet", showIcon: false),
                          ),
                        ),
                      ),
                    )
                  : Expanded(child: suggestionsList()),
            ],
          );
        }
        
        // Failed
        if (snapshot.data == AppSuggestionsState.Failed) {
          return AppCommonWidgets.errorWidget(onTap: () {
            appSuggestionsBloc.init();
          });
        }
        
        return AppCommonWidgets.appCircularProgress();
      },
    );
  }

//region Search field
  Widget searchField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: AppSearchField(
              textEditingController: appSuggestionsBloc.searchFieldTextCtrl,
              hintText: "Search suggestions with id, keywords",
              onChangeText: (v) {
                appSuggestionsBloc.onSearch();
              },
              onTapSuffix: () {
                appSuggestionsBloc.onSearch();
              },
              isAutoFocus: false,
              isActive: true,
            ),
          ),
          horizontalSizedBox(10),
          InkWell(
              onTap: () {
                // Add filter functionality if needed
                // appSuggestionsBloc.onTapFilter();
              },
              child: SvgPicture.asset(AppImages.filter2))
        ],
      ),
    );
  }
//endregion

//region Suggestions list
  Widget suggestionsList() {
    return Scrollbar(
      child: RefreshIndicator(
        color: AppColors.brandBlack,
        onRefresh: () async {
          await appSuggestionsBloc.init();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: appSuggestionsBloc.finalFilteredSuggestionsList.length,
            itemBuilder: (BuildContext context, int index) {
              return InkWell(
                onTap: () {
                  appSuggestionsBloc.goToSuggestionDetail(
                    suggestionDetail: appSuggestionsBloc.finalFilteredSuggestionsList[index],
                    suggestionId: appSuggestionsBloc.finalFilteredSuggestionsList[index].feedbackId!,
                  );
                },
                child: SupportScreenCommonWidgets.allFeedbackCard(
                  feedbackDetail: appSuggestionsBloc.finalFilteredSuggestionsList[index],
                  onTapVote: () {
                    appSuggestionsBloc.onTapUpVote(
                      rootSuggestion: appSuggestionsBloc.finalFilteredSuggestionsList[index],
                    );
                  },
                  onTapRightArrow: () {
                    // Handle right arrow tap if needed
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }

//endregion
}
