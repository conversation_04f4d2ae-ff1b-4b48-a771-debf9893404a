import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum AppSuggestionsState { Loading, Success, Failed }

class AppSuggestionsBloc {
  // region Common Variables
  late BuildContext context;
  final String entityReference;
  final AddSupportServices addSupportServices = AddSupportServices();

  // Controllers
  final appSuggestionsCtrl = StreamController<AppSuggestionsState>.broadcast();
  final TextEditingController searchFieldTextCtrl = TextEditingController();

  // Data
  late GetAllFeedbackResponse getAllSuggestionsResponse;
  List<FeedbackDetail> rootSuggestionsList = [];
  List<FeedbackDetail> finalFilteredSuggestionsList = [];

  // endregion

  // region Constructor
  AppSuggestionsBloc(this.context, this.entityReference);
  // endregion

  // region Init
  Future<void> init() async {
    await getAllAppSuggestions();
  }
  // endregion

  // region Get all app suggestions (SUGGESTION type)
  Future<void> getAllAppSuggestions() async {
    try {
      // Loading state
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Loading);
      
      // API call to get suggestions using SUGGESTION type
      getAllSuggestionsResponse = await addSupportServices.getTicketsByType(
        type: 'SUGGESTION',
        entityReference: entityReference,
      );
      
      // Calculate day difference
      calculateDayDifference();
      
      // Clear and populate root list
      rootSuggestionsList.clear();
      rootSuggestionsList.addAll(getAllSuggestionsResponse.feedbackDetailList ?? []);
      
      // Apply search filter
      applyFilter();
      
      // Success state
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Success);
    } on ApiErrorResponseMessage {
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Calculate day difference
  void calculateDayDifference() {
    for (var element in getAllSuggestionsResponse.feedbackDetailList ?? []) {
      if (element.date != null) {
        element.dayDifference = int.parse(CommonMethods.dateTimeAmPm(date: element.date!)[0]);
      }
    }
  }
  // endregion

  // region Apply filter
  void applyFilter() {
    finalFilteredSuggestionsList.clear();
    finalFilteredSuggestionsList.addAll(rootSuggestionsList);
  }
  // endregion

  // region On search
  void onSearch() {
    //If field is empty then store Default data
    if (searchFieldTextCtrl.text.isEmpty) {
      //Clear searched suggestions
      finalFilteredSuggestionsList.clear();
      //Add all data to finalFilteredSuggestionsList
      finalFilteredSuggestionsList.addAll(rootSuggestionsList);
      //Success state
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Success);
    }
    //Clear searched suggestions
    finalFilteredSuggestionsList.clear();
    //Search the text field data and filter
    for (var data in rootSuggestionsList) {
      if (data.brief!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.details!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.name!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.feedbackId!.toString().contains(searchFieldTextCtrl.text.toLowerCase())) {
        finalFilteredSuggestionsList.add(data);
      }
    }
    //Success state
    appSuggestionsCtrl.sink.add(AppSuggestionsState.Success);
  }
  // endregion

  // region Go to suggestion detail
  void goToSuggestionDetail({required FeedbackDetail suggestionDetail, required int suggestionId}) {
    var screen = FeedbackItemScreen(
      feedbackId: suggestionId,
      isAdmin: AppConstants.adminUserReference.contains(AppConstants.appData.userReference!),
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // Refresh screen
      appSuggestionsCtrl.sink.add(AppSuggestionsState.Success);
    });
  }
  // endregion

  // region On tap upvote
  void onTapUpVote({required FeedbackDetail rootSuggestion}) {
    // Handle upvote logic if needed
    addVoteApiCall(rootSuggestion: rootSuggestion);
  }
  // endregion

  // region Add vote API
  Future<void> addVoteApiCall({required FeedbackDetail rootSuggestion}) async {
    try {
      await addSupportServices.addVote(feedbackId: rootSuggestion.feedbackId!);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Dispose
  void dispose() {
    appSuggestionsCtrl.close();
    searchFieldTextCtrl.dispose();
  }
  // endregion
}
