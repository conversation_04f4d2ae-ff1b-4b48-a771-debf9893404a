import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/support/give_feedback/give_feedback_bloc.dart';
import 'package:swadesic/features/support/support_common_widgets.dart';
import 'package:swadesic/features/support/widgets/target_reference_search_widget.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Give feedback screen
class GiveFeedback extends StatefulWidget {

  final bool? isReport;
  final String? targetStoreReference;
  final String? targetStoreHandle;

  const GiveFeedback({
    Key? key,
    this.isReport = true,
    this.targetStoreReference,
    this.targetStoreHandle,
  }) : super(key: key);

  @override
  GiveFeedbackState createState() => GiveFeedbackState();
}
// endregion

class GiveFeedbackState extends State<GiveFeedback> {
  // region Bloc
  late GiveFeedbackBloc giveFeedbackBloc;
  SuggestionItem? selectedTarget;
  // endregion

  // region Init
  @override
  void initState() {
    giveFeedbackBloc = GiveFeedbackBloc(context,widget.isReport!);
    giveFeedbackBloc.init();

    // Initialize target if provided from store menu
    if (widget.targetStoreReference != null && widget.targetStoreHandle != null) {
      selectedTarget = SuggestionItem(
        type: 'STORE',
        reference: widget.targetStoreReference,
        primaryText: widget.targetStoreHandle,
        secondaryText: null,
        imageUrl: null,
      );
    }

    super.initState();
  }

  // endregion

  // region Helper methods
  /// Determines if the target reference field should be shown
  /// Target field should be shown when:
  /// 1. "Report an issue" is selected AND general support access (targetStoreReference is null) - user can select any target
  /// 2. "Report an issue" is selected AND user viewing a store's support (targetStoreReference provided) - target is pre-filled
  /// 3. "Report an issue" is selected AND store viewing another store's support (targetStoreReference provided) - target is pre-filled
  /// Target field should NOT be shown when:
  /// 1. "Suggest an improvement" is selected (regardless of context)
  /// 2. "Report an issue" is selected AND store creating ticket for itself (accessed from own store support, targetStoreReference is null)
  bool _shouldShowTargetField() {
    // First check if "Suggest an improvement" is selected - never show target field for suggestions
    if (!giveFeedbackBloc.isReport!) {
      return false;
    }

    // Only show target field for "Report an issue" option
    // If target store reference is provided, this means we're accessing support from a specific store
    // In this case, always show the target field (it will be pre-filled and possibly disabled)
    if (widget.targetStoreReference != null) {
      return true;
    }

    // If no target store reference is provided, we need to determine the context:
    // - If it's a user, show target field for general support
    // - If it's a store viewing its own support, don't show target field
    if (AppConstants.appData.isUserView!) {
      // User accessing general support - show target field for selection
      return true;
    }

    // Store accessing its own support (no target reference) - don't show target field
    return false;
  }
  // endregion

  //region Dispose
  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    imageCache.clear();
    // TODO: implement dispose
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
      return StreamBuilder<GivefeedbackState>(
        stream: giveFeedbackBloc.giveFeedbackCtrl.stream,
        builder: (context, snapshot) {
          if(snapshot.data == GivefeedbackState.Success){
            return  body();
          }
          if(snapshot.data == GivefeedbackState.Loading){
            return AppCommonWidgets.appCircularProgress();
          }
          return  body();
        }
      );
  }

  // endregion


  //region Body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: SingleChildScrollView(
        child: Column(
          children: [
            issueAndSuggestion(),
            verticalSizedBox(30),
            category(),
            verticalSizedBox(30),
            // Only show target field when "Report an issue" is selected and appropriate context
            if (_shouldShowTargetField()) ...[
              targetReferenceField(),
              verticalSizedBox(30),
            ],
            title(),
            verticalSizedBox(30),
            screenDescription(),
            attachment(),
            verticalSizedBox(30),
            subMit(),
            AppCommonWidgets.bottomListSpace(context: context),

          ],
        ),
      ),
    );
  }

  // endregion


  //region Issue and suggestion
  Widget issueAndSuggestion(){
    // Determine if accessed from store menu
    bool isAccessedFromStoreMenu = widget.targetStoreReference != null;

    // Determine subtitle for "Report an issue" based on context
    String reportSubtitle = isAccessedFromStoreMenu
        ? "Report an issue with this store or your experience with them"
        : "Something in app is not working as expected or broken";

    return Column(
      children: [
        SupportScreenCommonWidgets.issueAndSuggestionRadio(
          title: "Report an issue",
          subTitle: reportSubtitle,
          isActive: giveFeedbackBloc.isReport!,
          onPress: (){
            giveFeedbackBloc.onSelectSupportOrSuggestion(data:true);
          }
        ),
        // Only show "Suggest an improvement" option when NOT accessed from store menu
        if (!isAccessedFromStoreMenu) ...[
          verticalSizedBox(10),
          SupportScreenCommonWidgets.issueAndSuggestionRadio(
            title: "Suggest an improvement",
            subTitle: "New ideas or desired enhancements for this app or general experience",
            isActive: !giveFeedbackBloc.isReport!,
            onPress: (){
              giveFeedbackBloc.onSelectSupportOrSuggestion(data:false);
            }
          ),
        ],
      ],
    );
  }
  //endregion


  //region Category
  Widget category(){
    return Column(
      children: [

        AppTitleAndOptions(
          title: AppStrings.category,
          option:AppCommonWidgets.dropDownOptions(
              onTap: () {
                giveFeedbackBloc.onTapCategory();
              },
              context: context,
              hintText: AppStrings.selectTheCategory,
              value:giveFeedbackBloc.selectedCategory),
        ),
      ],

    );
  }
  //endregion


//region Title
Widget title(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.title,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: AppTextFields.allTextField(
            context: context,
            maxEntry: 200,
            maxLines: 2,
            textEditingController: giveFeedbackBloc.titleTextCtrl,
            hintText:  AppStrings.giveUsABrief,
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(vertical: 10),
        //   child: Row(
        //     mainAxisSize: MainAxisSize.max,
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     children: [
        //       SupportScreenCommonWidgets.title(title: "Title"),
        //       SvgPicture.asset(AppImages.exclamation),
        //     ],
        //   ),
        // ),
        // colorFilledTextField(
        //     context: context,
        //     textFieldCtrl:giveFeedbackBloc.titleTextCtrl,
        //     hintText: "Give us a brief with keywords",
        //     textFieldMaxLine:3,
        //     keyboardType: TextInputType.multiline,
        //     textInputAction: TextInputAction.next,
        //     regExp: AppConstants.acceptAll,
        //     fieldTextCapitalization:TextCapitalization.sentences,
        //   maxCharacter: 100,
        //   hintFontSize: 12,
        //   hintFontFamily: AppConstants.rRegular,
        //   hintTextColor: AppColors.writingColor3,
        // ),
      ],
    );

}
//endregion



  //region Screen description
  Widget screenDescription(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        AppTitleAndOptions(
          title: AppStrings.description,
          option: AppTextFields.allTextField(
            context: context,
            maxEntry: 1000,
            minLines: 5,
            maxLines: 5,
            textInputAction:TextInputAction.newline ,
            keyboardType:TextInputType.multiline ,
            textEditingController: giveFeedbackBloc.descTextCtrl,
            hintText:  AppStrings.pleaseBeAsDetailedAsPossible,
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(vertical: 10),
        //   child: SupportScreenCommonWidgets.title(title: "Description"),
        // ),
        // colorFilledTextField(
        //   context: context,
        //   textFieldCtrl:giveFeedbackBloc.descTextCtrl,
        //   hintText: "Please be as detailed as possible. What did you expect and what happened instead?",
        //   textFieldMaxLine:10,
        //   maxCharacter: 2000,
        //   keyboardType: TextInputType.text,
        //   textInputAction: TextInputAction.next,
        //   regExp: AppConstants.acceptAll,
        //   minLines: 5,
        //   fieldTextCapitalization:TextCapitalization.sentences,
        //   hintFontSize: 12,
        //   hintFontFamily: AppConstants.rRegular,
        //   hintTextColor: AppColors.writingColor3,
        // ),
      ],
    );

  }
//endregion

//region Attachment
Widget attachment(){
    return Column(
      children: [
        InkWell(
          onTap: (){
            giveFeedbackBloc.onTapAddFile();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
            decoration: const BoxDecoration(
              color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.all(Radius.circular(10)),
            ),

            child: Row(
              children: [
                SvgPicture.asset(AppImages.plus,color: AppColors.appBlack,),
                horizontalSizedBox(10),
                 Text("Upload screenshots, images, videos or docs",style:AppTextStyle.access0(textColor: AppColors.appBlack),),
              ],
            ),
          ),
        ),



        giveFeedbackBloc.files.isEmpty?const SizedBox(): Row(
          children: [
            Expanded(
              child: SizedBox(height: 100,
              child: ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: giveFeedbackBloc.files.length,
                  itemBuilder: (context,index){
                  return Stack(
                    alignment: Alignment.topRight,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: imageAndDocument(data: giveFeedbackBloc.files[index]),
                      ),
                      Positioned(
                          top: 2,
                          right:2,
                          child: InkWell(
                              onTap: (){
                                //print(index);
                                giveFeedbackBloc.onTapRemoveFile(data: giveFeedbackBloc.files[index]);
                              },
                              child: SvgPicture.asset(AppImages.removeCircle)))
                    ],
                  );
                  }
              ),
              ),
            ),
            const SizedBox()
          ],
        )
      ],
    );
}
//endregion



  //region Image and document
  Widget imageAndDocument({required File data}){
///Split file name to identify the extension
String? fileType;
fileType = CommonMethods.returnExtension(file:data);
    if(giveFeedbackBloc.otherFileTypeList.contains(fileType)){
      return Container(
          width: 100,
          height: 100,
          color: AppColors.textFieldFill1,
          child: const Icon(Icons.file_copy));
    }
    return SizedBox(
        width: 100,
        height: 100,
        child: Image.file(data,cacheHeight: 800,cacheWidth: 800,fit: BoxFit.cover,));
  }
  //endregion



//region Target Reference Field
Widget targetReferenceField() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const AppTitleAndOptions(
        title: "Target",
        titlePaddingHorizontal: 0,
      ),
      // verticalSizedBox(10),
      TargetReferenceSearchWidget(
        onTargetSelected: (target) {
          setState(() {
            selectedTarget = target;
          });
        },
        initialTarget: selectedTarget,
        hintText: widget.targetStoreReference != null
            ? widget.targetStoreHandle
            : "Search users or stores...",
        isEnabled: widget.targetStoreReference == null, // Disabled when target is pre-set
      ),
    ],
  );
}
//endregion

//region Submit button
Widget subMit(){
    return SupportScreenCommonWidgets.subMitButton(buttonName:"Submit", onPressed:(){
      giveFeedbackBloc.addFeedback(targetReference: selectedTarget?.reference);
    });
}
//endregion



}
